import { univerPlugin } from "@univerjs/vite-plugin";
import { defineConfig, loadEnv } from "vite";
import packageJson from "./package.json" assert { type: "json" };
import remToPx from "postcss-rem-to-pixel";

export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return defineConfig({
    plugins: [univerPlugin()],
    css: {
      postcss: {
        plugins: [
          remToPx({
            rootValue: 16, // 根字体大小，通常是16px
            unitPrecision: 5, // 保留小数位数
            propList: ["*"], // 转换所有属性
            selectorBlackList: [], // 不转换的选择器
            replace: true, // 是否替换而不是添加
            mediaQuery: false, // 是否转换媒体查询中的rem
            minRemValue: 0, // 最小转换值
          }),
        ],
      },
    },
    define: {
      "process.env.UNIVER_CLIENT_LICENSE":
        `"${env.UNIVER_CLIENT_LICENSE}"` ||
        '"%%UNIVER_CLIENT_LICENSE_PLACEHOLDER%%"',
      "process.env.UNIVER_VERSION": `"${packageJson.dependencies["@univerjs/presets"]}"`,
    },
    build: {
      // 禁用生成 HTML 文件
      rollupOptions: {
        // 如果你想完全控制输出，可以指定入口文件
        input: {
          main: "./src/main.js",
        },
        output: {
          // 设置输出格式为 IIFE，这样就不需要 type="module"
          format: "iife",
          // 设置全局变量名，这样可以通过 window.UniverApp 访问
          name: "UniverApp",
          // 直接输出到根目录，分别生成 JS 和 CSS
          entryFileNames: "index.js",
          chunkFileNames: "index-[name].js",
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split(".");
            const ext = info[info.length - 1];
            if (/\.(css)$/.test(assetInfo.name)) {
              return `index.${ext}`;
            }
            if (/\.(png|jpe?g|gif|svg|ico|webp)$/.test(assetInfo.name)) {
              return `images/[name].${ext}`;
            }
            if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
              return `fonts/[name].${ext}`;
            }
            return `[name].${ext}`;
          },
        },
      },
      // 禁用生成 index.html
      emptyOutDir: false, // 不清空输出目录，保留第一次构建的结果
      // 确保 CSS 文件单独输出
      cssCodeSplit: false,
      // 减少内存使用
      minify: false, // 第二次构建时不压缩，因为主要是处理CSS
      sourcemap: false, // 不生成sourcemap
      // 输出到不同目录避免冲突
      outDir: "dist",
    },
  });
};

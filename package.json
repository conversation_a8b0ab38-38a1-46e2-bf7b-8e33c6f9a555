{"name": "univer-sheet-start-kit", "type": "module", "version": "0.9.1", "private": true, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417", "description": "A starter kit for Univer Sheet", "scripts": {"dev": "vite --host", "build": "vite build"}, "dependencies": {"@univerjs/presets": "^0.9.4", "@univerjs/preset-sheets-core": "^0.9.4", "@univerjs/find-replace": "^0.9.4", "@univerjs/preset-sheets-data-validation": "^0.9.4", "@univerjs/preset-sheets-filter": "^0.9.4", "@univerjs/preset-sheets-find-replace": "^0.9.4", "@univerjs/preset-sheets-note": "^0.9.4", "@univerjs/preset-sheets-sort": "^0.9.4", "install": "^0.13.0", "npm": "^11.5.1"}, "devDependencies": {"@univerjs/vite-plugin": "^0.5.1", "postcss-rem-to-pixel": "^4.1.2", "typescript": "^5.8.3", "vite": "^6.3.5"}}
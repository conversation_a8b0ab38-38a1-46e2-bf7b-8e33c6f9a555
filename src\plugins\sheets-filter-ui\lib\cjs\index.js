"use strict";var nt=Object.defineProperty;var ot=(t,e,r)=>e in t?nt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var v=(t,e,r)=>ot(t,typeof e!="symbol"?e+"":e,r);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const u=require("@univerjs/core"),c=require("@univerjs/sheets-filter"),M=require("@univerjs/sheets-ui"),T=require("@univerjs/ui"),z=require("@univerjs/engine-render"),I=require("@univerjs/sheets"),S=require("rxjs"),Fe=require("@univerjs/rpc"),b=require("@univerjs/design"),m=require("react/jsx-runtime"),O=require("react");var P=(t=>(t[t.FIRST=0]="FIRST",t[t.SECOND=1]="SECOND",t))(P||{}),C=(t=>(t.NONE="none",t.STARTS_WITH="startsWith",t.DOES_NOT_START_WITH="doesNotStartWith",t.ENDS_WITH="endsWith",t.DOES_NOT_END_WITH="doesNotEndWith",t.CONTAINS="contains",t.DOES_NOT_CONTAIN="doesNotContain",t.EQUALS="equals",t.NOT_EQUALS="notEquals",t.EMPTY="empty",t.NOT_EMPTY="notEmpty",t.BETWEEN="between",t.NOT_BETWEEN="notBetween",t.CUSTOM="custom",t))(C||{}),f;(t=>{t.NONE={label:"sheets-filter.conditions.none",operator:C.NONE,order:P.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.NONE]: should not have initial form params!")},testMappingParams:i=>i.operator1===C.NONE,mapToFilterColumn:()=>null,testMappingFilterColumn:i=>!i.customFilters&&!i.filters?{}:!1},t.EMPTY={label:"sheets-filter.conditions.empty",operator:C.EMPTY,order:P.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.EMPTY]: should not have initial form params!")},testMappingParams:({operator1:i})=>i===C.EMPTY,mapToFilterColumn:()=>({customFilters:{customFilters:[{val:""}]}}),testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.val===""&&o.operator===void 0?{operator1:C.EMPTY}:!1}},t.NOT_EMPTY={label:"sheets-filter.conditions.not-empty",operator:C.NOT_EMPTY,order:P.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.NOT_EMPTY]: should not have initial form params!")},testMappingParams:({operator1:i})=>i===C.NOT_EMPTY,mapToFilterColumn:()=>({customFilters:{customFilters:[{val:"",operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.val===" "&&o.operator===c.CustomFilterOperator.NOT_EQUALS?{operator1:C.NOT_EMPTY}:!1}},t.TEXT_CONTAINS={label:"sheets-filter.conditions.text-contains",operator:C.CONTAINS,order:P.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:C.CONTAINS,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===C.CONTAINS},mapToFilterColumn:i=>{const{val1:o}=i;return o===""?null:{customFilters:{customFilters:[{val:`*${o}*`}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0],l=o.val.toString();return!o.operator&&l.startsWith("*")&&l.endsWith("*")?{operator1:C.CONTAINS,val1:l.slice(1,-1)}:!1}},t.DOES_NOT_CONTAIN={label:"sheets-filter.conditions.does-not-contain",operator:C.DOES_NOT_CONTAIN,order:P.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:C.DOES_NOT_CONTAIN,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`*${i.val1}*`,operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingParams:i=>{const[o]=B(i);return o===C.DOES_NOT_CONTAIN},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0],l=o.val.toString();return o.operator===c.CustomFilterOperator.NOT_EQUALS&&l.startsWith("*")&&l.endsWith("*")?{operator1:C.DOES_NOT_CONTAIN,val1:l.slice(1,-1)}:!1}},t.STARTS_WITH={label:"sheets-filter.conditions.starts-with",operator:C.STARTS_WITH,order:P.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:C.STARTS_WITH,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`${i.val1}*`}]}}),testMappingParams:i=>{const[o]=B(i);return o===C.STARTS_WITH},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0],l=o.val.toString();return!o.operator&&l.endsWith("*")&&!l.startsWith("*")?{operator1:C.STARTS_WITH,val1:l.slice(0,-1)}:!1}},t.ENDS_WITH={label:"sheets-filter.conditions.ends-with",operator:C.ENDS_WITH,order:P.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:C.ENDS_WITH,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`*${i.val1}`}]}}),testMappingParams:i=>{const[o]=B(i);return o===C.ENDS_WITH},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0],l=o.val.toString();return!o.operator&&l.startsWith("*")&&!l.endsWith("*")?{operator1:C.ENDS_WITH,val1:l.slice(1)}:!1}},t.EQUALS={label:"sheets-filter.conditions.equals",operator:C.EQUALS,order:P.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:C.EQUALS,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===C.EQUALS},mapToFilterColumn:i=>{const{val1:o}=i;return o===""?null:{customFilters:{customFilters:[{val:o}]}}},testMappingFilterColumn:i=>{var o,l,h;return((l=(o=i.filters)==null?void 0:o.filters)==null?void 0:l.length)===1?{operator1:C.EQUALS,val1:""}:((h=i.customFilters)==null?void 0:h.customFilters.length)===1&&!i.customFilters.customFilters[0].operator?{operator1:C.EQUALS,val1:i.customFilters.customFilters[0].val.toString()}:!1}},t.GREATER_THAN={label:"sheets-filter.conditions.greater-than",operator:c.CustomFilterOperator.GREATER_THAN,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.GREATER_THAN,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.GREATER_THAN}]}}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.GREATER_THAN},testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.GREATER_THAN?!1:{operator1:c.CustomFilterOperator.GREATER_THAN,val1:o.val.toString()}}},t.GREATER_THAN_OR_EQUAL={label:"sheets-filter.conditions.greater-than-or-equal",operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL}]}}),testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.GREATER_THAN_OR_EQUAL?!1:{operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:o.val.toString()}}},t.LESS_THAN={label:"sheets-filter.conditions.less-than",operator:c.CustomFilterOperator.LESS_THAN,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.LESS_THAN},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.LESS_THAN}]}}),testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.LESS_THAN?!1:{operator1:c.CustomFilterOperator.LESS_THAN,val1:o.val.toString()}}},t.LESS_THAN_OR_EQUAL={label:"sheets-filter.conditions.less-than-or-equal",operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.LESS_THAN_OR_EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL}]}}),testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.LESS_THAN_OR_EQUAL?!1:{operator1:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val1:o.val.toString()}}},t.EQUAL={label:"sheets-filter.conditions.equal",operator:c.CustomFilterOperator.EQUAL,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.EQUAL,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.EQUAL}]}}),testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.EQUAL?!1:{operator1:c.CustomFilterOperator.EQUAL,val1:o.val.toString()}}},t.NOT_EQUAL={label:"sheets-filter.conditions.not-equal",operator:c.CustomFilterOperator.NOT_EQUALS,numOfParameters:1,order:P.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.NOT_EQUALS,val1:""}),testMappingParams:i=>{const[o]=B(i);return o===c.CustomFilterOperator.NOT_EQUALS},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingFilterColumn:i=>{var l;if(((l=i.customFilters)==null?void 0:l.customFilters.length)!==1)return!1;const o=i.customFilters.customFilters[0];return o.operator!==c.CustomFilterOperator.NOT_EQUALS?!1:{operator1:c.CustomFilterOperator.NOT_EQUALS,val1:o.val.toString()}}},t.BETWEEN={label:"sheets-filter.conditions.between",operator:C.BETWEEN,order:P.SECOND,numOfParameters:2,getDefaultFormParams:()=>({and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:"",operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:""}),testMappingParams:i=>{const{and:o,operator1:l,operator2:h}=i;if(!o)return!1;const d=[l,h];return d.includes(c.CustomFilterOperator.GREATER_THAN_OR_EQUAL)&&d.includes(c.CustomFilterOperator.LESS_THAN_OR_EQUAL)},mapToFilterColumn:i=>{const{val1:o,val2:l,operator1:h}=i,d=h===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL;return{customFilters:{and:u.BooleanNumber.TRUE,customFilters:[{val:d?o:l,operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL},{val:d?l:o,operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const[o,l]=i.customFilters.customFilters;return o.operator===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL&&l.operator===c.CustomFilterOperator.LESS_THAN_OR_EQUAL&&i.customFilters.and?{and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:o.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:l.val.toString()}:l.operator===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL&&o.operator===c.CustomFilterOperator.LESS_THAN_OR_EQUAL&&i.customFilters.and?{and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:l.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:o.val.toLocaleString()}:!1}},t.NOT_BETWEEN={label:"sheets-filter.conditions.not-between",operator:C.NOT_BETWEEN,order:P.SECOND,numOfParameters:2,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN,val1:"",operator2:c.CustomFilterOperator.GREATER_THAN,val2:""}),testMappingParams:i=>{const{and:o,operator1:l,operator2:h}=i;if(o)return!1;const d=[l,h];return d.includes(c.CustomFilterOperator.GREATER_THAN)&&d.includes(c.CustomFilterOperator.LESS_THAN)},mapToFilterColumn:i=>{const{val1:o,val2:l,operator1:h}=i,d=h===c.CustomFilterOperator.GREATER_THAN;return{customFilters:{customFilters:[{val:d?o:l,operator:c.CustomFilterOperator.GREATER_THAN},{val:d?l:o,operator:c.CustomFilterOperator.LESS_THAN}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const[o,l]=i.customFilters.customFilters;return o.operator===c.CustomFilterOperator.LESS_THAN&&l.operator===c.CustomFilterOperator.GREATER_THAN&&!i.customFilters.and?{operator1:c.CustomFilterOperator.LESS_THAN,val1:o.val.toString(),operator2:c.CustomFilterOperator.GREATER_THAN,val2:l.val.toString()}:l.operator===c.CustomFilterOperator.LESS_THAN&&o.operator===c.CustomFilterOperator.GREATER_THAN&&!i.customFilters.and?{operator1:c.CustomFilterOperator.GREATER_THAN,val1:l.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN,val2:o.val.toLocaleString()}:!1}},t.CUSTOM={label:"sheets-filter.conditions.custom",operator:C.CUSTOM,order:P.SECOND,numOfParameters:2,getDefaultFormParams:()=>({operator1:C.NONE,val1:"",operator2:C.NONE,val2:""}),testMappingParams:()=>!0,mapToFilterColumn:i=>{const{and:o,val1:l,val2:h,operator1:d,operator2:p}=i;function E(w,R){for(const x of t.ALL_CONDITIONS)if(x.operator===w)return x.mapToFilterColumn({val1:R,operator1:w})}const _=!d||d===t.NONE.operator,g=!p||p===t.NONE.operator;if(_&&g)return t.NONE.mapToFilterColumn({});if(_)return E(p,h);if(g)return E(d,l);const F=E(d,l),y=E(p,h),N={customFilters:[F.customFilters.customFilters[0],y.customFilters.customFilters[0]]};return o&&(N.and=u.BooleanNumber.TRUE),{customFilters:N}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const o=i.customFilters.customFilters.map(d=>a({customFilters:{customFilters:[d]}})),l={operator1:o[0][0].operator,val1:o[0][1].val1,operator2:o[1][0].operator,val2:o[1][1].val1};return i.customFilters.and&&(l.and=!0),l}},t.ALL_CONDITIONS=[t.NONE,t.EMPTY,t.NOT_EMPTY,t.TEXT_CONTAINS,t.DOES_NOT_CONTAIN,t.STARTS_WITH,t.ENDS_WITH,t.EQUALS,t.GREATER_THAN,t.GREATER_THAN_OR_EQUAL,t.LESS_THAN,t.LESS_THAN_OR_EQUAL,t.EQUAL,t.NOT_EQUAL,t.BETWEEN,t.NOT_BETWEEN,t.CUSTOM];function e(i){const o=t.ALL_CONDITIONS.find(l=>l.operator===i);if(!o)throw new Error(`[SheetsFilter]: no condition item found for operator: ${i}`);return o}t.getItemByOperator=e;function r(i,o){for(const l of t.ALL_CONDITIONS.filter(h=>h.numOfParameters===o))if(l.numOfParameters!==0&&l.testMappingParams(i))return l;for(const l of t.ALL_CONDITIONS)if(l.testMappingParams(i))return l;throw new Error("[SheetsFilter]: no condition item can be mapped from the filter map params!")}t.testMappingParams=r;function s(i){const o=t.ALL_CONDITIONS.find(l=>l.operator===i);return(o==null?void 0:o.numOfParameters)===0?{operator1:o.operator}:o.getDefaultFormParams()}t.getInitialFormParams=s;function n(i,o){return i.mapToFilterColumn(o)}t.mapToFilterColumn=n;function a(i){if(!i)return[t.NONE,{}];for(const o of t.ALL_CONDITIONS){const l=o.testMappingFilterColumn(i);if(l)return[o,l]}return[t.NONE,{}]}t.testMappingFilterColumn=a})(f||(f={}));function B(t){const{operator1:e,operator2:r,val1:s,val2:n}=t;if(e&&r)throw new Error("Both operator1 and operator2 are set!");if(!e&&!r)throw new Error("Neither operator1 and operator2 and both not set!");return e?[e,s]:[r,n]}function Re(t){const e=[],r=[];let s=0,n=0;function a(i){i.leaf&&(i.checked?(e.push(i),s+=i.count):(r.push(i),n+=i.count)),i.children&&i.children.forEach(a)}return t.forEach(a),{checkedItems:e,uncheckedItems:r,checked:s,unchecked:n}}var lt=Object.getOwnPropertyDescriptor,at=(t,e,r,s)=>{for(var n=s>1?void 0:s?lt(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},Ie=(t,e)=>(r,s)=>e(r,s,t);const Le="sheets-filter.generate-filter-values.service",fe=u.createIdentifier(Le),ct=["yyyy-mm-dd","yyyy-mm-dd;@","yyyy/mm/dd;@","yyyy/mm/dd hh:mm","yyyy-m-d am/pm h:mm","yyyy-MM-dd","yyyy/MM/dd","yyyy/mm/dd",'yyyy"年"MM"月"dd"日"',"MM-dd",'M"月"d"日"',"MM-dd A/P hh:mm"];let Pe=class extends u.Disposable{constructor(t,e,r){super(),this._localeService=t,this._univerInstanceService=e,this._logService=r}async getFilterValues(t){var p;const{unitId:e,subUnitId:r,filteredOutRowsByOtherColumns:s,filterColumn:n,filters:a,blankChecked:i,iterateRange:o,alreadyChecked:l}=t,h=this._univerInstanceService.getUnit(e),d=(p=this._univerInstanceService.getUnit(e))==null?void 0:p.getSheetBySheetId(r);return!h||!d?[]:(this._logService.debug("[SheetsGenerateFilterValuesService]","getFilterValues for",{unitId:e,subUnitId:r}),Qe(a,this._localeService,o,d,new Set(s),n,new Set(l.map(String)),i,h.getStyles()))}};Pe=at([Ie(0,u.Inject(u.LocaleService)),Ie(1,u.IUniverInstanceService),Ie(2,u.ILogService)],Pe);function Qe(t,e,r,s,n,a,i,o,l){var N,w,R,x,$,q,J,ee,H,U;const h=new Map,d=new Map,p="yyyy-mm-dd",E=new Set(ct),_="empty",g=!t&&((a==null?void 0:a.filterBy)===c.FilterBy.COLORS||(a==null?void 0:a.filterBy)===c.FilterBy.CONDITIONS)&&((N=a.filteredOutRows)==null?void 0:N.size);let F=0;for(const k of s.iterateByColumn(r,!1,!1)){const{row:rt,rowSpan:$e=1}=k;let te=0;for(;te<$e;){const it=rt+te;if(n.has(it)){te++;continue}const K=k!=null&&k.value?u.extractPureTextFromCell(k.value):"";if(!K){F+=1,te+=$e;continue}const me=(w=k.value)!=null&&w.v&&!k.value.p?($=(x=l.get((R=k.value)==null?void 0:R.s))==null?void 0:x.n)==null?void 0:$.pattern:"",st=me&&u.numfmt.getFormatInfo(me).isDate;if(me&&st&&E.has(me)){const Q=(q=s.getCellRaw(k.row,k.col))==null?void 0:q.v;if(!Q){te++;continue}const re=u.numfmt.format(p,Q),[A,D,le]=re.split("-").map(Number);let Z=h.get(`${A}`);Z||(Z={title:`${A}`,key:`${A}`,children:[],count:0,leaf:!1,checked:!1},h.set(`${A}`,Z),d.set(`${A}`,[`${A}`]));let W=(J=Z.children)==null?void 0:J.find(Oe=>Oe.key===`${A}-${D}`);W||(W={title:e.t(`sheets-filter.date.${D}`),key:`${A}-${D}`,children:[],count:0,leaf:!1,checked:!1},(ee=Z.children)==null||ee.push(W),d.set(`${A}-${D}`,[`${A}`,`${A}-${D}`]));const Ee=(H=W==null?void 0:W.children)==null?void 0:H.find(Oe=>Oe.key===`${A}-${D}-${le}`);Ee?(Ee.originValues.add(K),Ee.count++,W.count++,Z.count++):((U=W.children)==null||U.push({title:`${le}`,key:`${A}-${D}-${le}`,count:1,originValues:new Set([K]),leaf:!0,checked:g?!1:i.size?i.has(K):!o}),W.count++,Z.count++,d.set(`${A}-${D}-${le}`,[`${A}`,`${A}-${D}`,`${A}-${D}-${le}`]))}else{const Q=K;let re=h.get(Q);re?re.count++:(re={title:K,leaf:!0,checked:g?!1:i.size?i.has(K):!o,key:Q,count:1},h.set(Q,re),d.set(Q,[Q]))}te++}}const y=g?!1:t?o:!0;if(F>0){const k={title:e.t("sheets-filter.panel.empty"),count:F,leaf:!0,checked:y,key:_};h.set("empty",k),d.set("empty",[_])}return{filterTreeItems:ut(Array.from(h.values())),filterTreeMapCache:d}}function ut(t){return Array.from(t).sort((e,r)=>e.children&&!r.children?-1:!e.children&&r.children?1:ht(e.title,r.title)).map(e=>(e.children&&e.children.sort((r,s)=>{const n=Number.parseInt(r.key.split("-")[1],10),a=Number.parseInt(s.key.split("-")[1],10);return n-a}).forEach(r=>{r.children&&r.children.sort((s,n)=>{const a=Number.parseInt(s.key.split("-")[2],10),i=Number.parseInt(n.key.split("-")[2],10);return a-i})}),e))}const Ue=t=>!Number.isNaN(Number(t))&&!Number.isNaN(Number.parseFloat(t));function ht(t,e){const r=Ue(t),s=Ue(e);return r&&s?Number.parseFloat(t)-Number.parseFloat(e):r&&!s?-1:!r&&s?1:t.localeCompare(e)}function Ae(t,e){for(const r of t){if(r.key===e)return r;if(r.children){const s=Ae(r.children,e);if(s)return s}}return null}function Ge(t){return t.leaf?t.checked:t.children?t.children.every(e=>Ge(e)):!0}function ae(t,e){t.leaf&&(e!==void 0?t.checked=e:t.checked=!t.checked),t.children&&t.children.forEach(r=>ae(r,e))}function Ye(t,e){const r=[];return t.forEach(s=>{const n=s.originValues?e.some(o=>Array.from(s.originValues).some(l=>l.toLowerCase().includes(o.toLowerCase()))):!1,a=!n&&e.some(o=>s.title.toLowerCase().includes(o.toLowerCase()));if(n||a)r.push({...s});else if(s.children){const o=Ye(s.children,e);if(o.length>0){const l=o.reduce((h,d)=>h+d.count,0);r.push({...s,count:l,children:o})}}}),r}var dt=Object.getOwnPropertyDescriptor,Te=(t,e,r,s)=>{for(var n=s>1?void 0:s?dt(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},ce=(t,e)=>(r,s)=>e(r,s,t);u.createIdentifier("sheets-filter-ui.sheets-filter-panel.service");let Y=class extends u.Disposable{constructor(e,r){super();v(this,"_filterBy$",new S.BehaviorSubject(c.FilterBy.VALUES));v(this,"filterBy$",this._filterBy$.asObservable());v(this,"_filterByModel$",new S.ReplaySubject(1));v(this,"filterByModel$",this._filterByModel$.asObservable());v(this,"_filterByModel",null);v(this,"_hasCriteria$",new S.BehaviorSubject(!1));v(this,"hasCriteria$",this._hasCriteria$.asObservable());v(this,"_filterModel",null);v(this,"_col$",new S.BehaviorSubject(-1));v(this,"col$",this._col$.asObservable());v(this,"_filterHeaderListener",null);this._injector=e,this._refRangeService=r}get filterBy(){return this._filterBy$.getValue()}get filterByModel(){return this._filterByModel}set filterByModel(e){this._filterByModel=e,this._filterByModel$.next(e)}get filterModel(){return this._filterModel}get col(){return this._col$.getValue()}dispose(){this._filterBy$.complete(),this._filterByModel$.complete(),this._hasCriteria$.complete()}setupCol(e,r){this.terminate(),this._filterModel=e,this._col$.next(r);const s=e.getFilterColumn(r);if(s){const n=s.getColumnData();if(n.customFilters){this._hasCriteria$.next(!0),this._setupByConditions(e,r);return}if(n.colorFilters){this._hasCriteria$.next(!0),this._setupByColors(e,r);return}if(n.filters){this._hasCriteria$.next(!0),this._setupByValues(e,r);return}this._hasCriteria$.next(!1),this._setupByValues(e,r);return}this._hasCriteria$.next(!1),this._setupByValues(e,r)}changeFilterBy(e){if(!this._filterModel||this.col===-1)return!1;switch(e){case c.FilterBy.VALUES:this._setupByValues(this._filterModel,this.col);break;case c.FilterBy.COLORS:this._setupByColors(this._filterModel,this.col);break;case c.FilterBy.CONDITIONS:this._setupByConditions(this._filterModel,this.col);break}return!0}terminate(){return this._filterModel=null,this._col$.next(-1),this._disposeFilterHeaderChangeListener(),!0}_disposeFilterHeaderChangeListener(){var e;(e=this._filterHeaderListener)==null||e.dispose(),this._filterHeaderListener=null}_listenToFilterHeaderChange(e,r){this._disposeFilterHeaderChangeListener();const s=e.unitId,n=e.subUnitId,a=e.getRange(),i={startColumn:r,startRow:a.startRow,endRow:a.startRow,endColumn:r};this._filterHeaderListener=this._refRangeService.watchRange(s,n,i,(o,l)=>{if(!l)this.terminate();else{const h=l.startColumn-o.startColumn;h!==0&&this._filterByModel.deltaCol(h)}})}async _setupByValues(e,r){this._disposePreviousModel();const s=e.getRange();if(s.startRow===s.endRow)return!1;const n=await _e.fromFilterColumn(this._injector,e,r);return this.filterByModel=n,this._filterBy$.next(c.FilterBy.VALUES),this._listenToFilterHeaderChange(e,r),!0}async _setupByColors(e,r){this._disposePreviousModel();const s=e.getRange();if(s.startRow===s.endRow)return!1;const n=await ge.fromFilterColumn(this._injector,e,r);return this.filterByModel=n,this._filterBy$.next(c.FilterBy.COLORS),this._listenToFilterHeaderChange(e,r),!0}_setupByConditions(e,r){this._disposePreviousModel();const s=e.getRange();if(s.startRow===s.endRow)return!1;const n=ve.fromFilterColumn(this._injector,e,r,e.getFilterColumn(r));return this.filterByModel=n,this._filterBy$.next(c.FilterBy.CONDITIONS),this._listenToFilterHeaderChange(e,r),!0}_disposePreviousModel(){var e;(e=this._filterByModel)==null||e.dispose(),this.filterByModel=null}};Y=Te([ce(0,u.Inject(u.Injector)),ce(1,u.Inject(I.RefRangeService))],Y);let ve=class extends u.Disposable{constructor(e,r,s,n,a){super();v(this,"canApply$",S.of(!0));v(this,"_conditionItem$");v(this,"conditionItem$");v(this,"_filterConditionFormParams$");v(this,"filterConditionFormParams$");this._filterModel=e,this.col=r,this._commandService=a,this._conditionItem$=new S.BehaviorSubject(s),this.conditionItem$=this._conditionItem$.asObservable(),this._filterConditionFormParams$=new S.BehaviorSubject(n),this.filterConditionFormParams$=this._filterConditionFormParams$.asObservable()}static fromFilterColumn(e,r,s,n){const[a,i]=f.testMappingFilterColumn(n==null?void 0:n.getColumnData());return e.createInstance(ve,r,s,a,i)}get conditionItem(){return this._conditionItem$.getValue()}get filterConditionFormParams(){return this._filterConditionFormParams$.getValue()}dispose(){super.dispose(),this._conditionItem$.complete(),this._filterConditionFormParams$.complete()}deltaCol(e){this.col+=e}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}async apply(){if(this._disposed)return!1;const e=f.mapToFilterColumn(this.conditionItem,this.filterConditionFormParams);return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:e})}onPrimaryConditionChange(e){const r=f.ALL_CONDITIONS.find(s=>s.operator===e);if(!r)throw new Error(`[ByConditionsModel]: condition item not found for operator: ${e}!`);this._conditionItem$.next(r),this._filterConditionFormParams$.next(f.getInitialFormParams(e))}onConditionFormChange(e){const r={...this.filterConditionFormParams,...e};if(r.and!==!0&&delete r.and,typeof e.and<"u"||typeof e.operator1<"u"||typeof e.operator2<"u"){const s=f.testMappingParams(r,this.conditionItem.numOfParameters);this._conditionItem$.next(s)}this._filterConditionFormParams$.next(r)}};ve=Te([ce(4,u.ICommandService)],ve);let _e=class extends u.Disposable{constructor(e,r,s,n,a){super();v(this,"_rawFilterItems$");v(this,"rawFilterItems$");v(this,"filterItems$");v(this,"_filterItems",[]);v(this,"_treeMapCache");v(this,"canApply$");v(this,"_manuallyUpdateFilterItems$");v(this,"_searchString$");v(this,"searchString$");this._filterModel=e,this.col=r,this._commandService=a,this._treeMapCache=n,this._searchString$=new S.BehaviorSubject(""),this.searchString$=this._searchString$.asObservable(),this._rawFilterItems$=new S.BehaviorSubject(s),this.rawFilterItems$=this._rawFilterItems$.asObservable(),this._manuallyUpdateFilterItems$=new S.Subject,this.filterItems$=S.merge(S.combineLatest([this._searchString$.pipe(S.throttleTime(500,void 0,{leading:!0,trailing:!0}),S.startWith(void 0)),this._rawFilterItems$]).pipe(S.map(([i,o])=>{if(!i)return o;const h=i.toLowerCase().split(/\s+/).filter(d=>!!d);return Ye(o,h)})),this._manuallyUpdateFilterItems$).pipe(S.shareReplay(1)),this.canApply$=this.filterItems$.pipe(S.map(i=>Re(i).checked>0)),this.disposeWithMe(this.filterItems$.subscribe(i=>this._filterItems=i))}static async fromFilterColumn(e,r,s){const n=e.get(u.IUniverInstanceService),a=e.get(u.LocaleService),i=e.get(fe,u.Quantity.OPTIONAL),{unitId:o,subUnitId:l}=r,h=n.getUniverSheetInstance(o);if(!h)throw new Error(`[ByValuesModel]: Workbook not found for filter model with unitId: ${o}!`);const d=h==null?void 0:h.getSheetBySheetId(l);if(!d)throw new Error(`[ByValuesModel]: Worksheet not found for filter model with unitId: ${o} and subUnitId: ${l}!`);const p=r.getRange(),E=s,_=r.getFilterColumn(s),g=_==null?void 0:_.getColumnData().filters,F=new Set(g==null?void 0:g.filters),y=!!(g&&g.blank),N=r.getFilteredOutRowsExceptCol(s),w={...p,startRow:p.startRow+1,startColumn:E,endColumn:E};let R,x;if(i){const $=await i.getFilterValues({unitId:o,subUnitId:l,filteredOutRowsByOtherColumns:Array.from(N),filterColumn:_,filters:!!g,blankChecked:y,iterateRange:w,alreadyChecked:Array.from(F)});R=$.filterTreeItems,x=$.filterTreeMapCache}else{const $=Qe(!!g,a,w,d,N,_,F,y,h.getStyles());R=$.filterTreeItems,x=$.filterTreeMapCache}return e.createInstance(_e,r,s,R,x)}get rawFilterItems(){return this._rawFilterItems$.getValue()}get filterItems(){return this._filterItems}get treeMapCache(){return this._treeMapCache}dispose(){this._rawFilterItems$.complete(),this._searchString$.complete()}deltaCol(e){this.col+=e}setSearchString(e){this._searchString$.next(e)}onCheckAllToggled(e){const r=u.Tools.deepClone(this._filterItems);r.forEach(s=>ae(s,e)),this._manuallyUpdateFilterItems(r)}onFilterCheckToggled(e){const r=u.Tools.deepClone(this._filterItems),s=Ae(r,e.key);if(!s)return;const n=Ge(s);ae(s,!n),this._manuallyUpdateFilterItems(r)}onFilterOnly(e){const r=u.Tools.deepClone(this._filterItems);r.forEach(s=>ae(s,!1)),e.forEach(s=>{const n=Ae(r,s);n&&ae(n,!0)}),this._manuallyUpdateFilterItems(r)}_manuallyUpdateFilterItems(e){this._manuallyUpdateFilterItems$.next(e)}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}async apply(){if(this._disposed)return!1;const e=Re(this._filterItems),{checked:r,checkedItems:s}=e,n=this.rawFilterItems;let a=0;for(const h of n)a+=h.count;const i=r===0,o=e.checked===a,l={colId:this.col};if(i)throw new Error("[ByValuesModel]: no checked items!");if(o)return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null});{l.filters={};const h=s.filter(p=>p.key!=="empty");h.length>0&&(l.filters={filters:h.flatMap(p=>p.originValues?Array.from(p.originValues):[p.title])}),h.length!==s.length&&(l.filters.blank=!0)}return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:l})}};_e=Te([ce(4,u.ICommandService)],_e);let ge=class extends u.Disposable{constructor(e,r,s,n,a){super();v(this,"canApply$",S.of(!0));v(this,"_cellFillColors$");v(this,"cellFillColors$");v(this,"_cellTextColors$");v(this,"cellTextColors$");this._filterModel=e,this.col=r,this._commandService=a,this._cellFillColors$=new S.BehaviorSubject(Array.from(s.values())),this.cellFillColors$=this._cellFillColors$.asObservable(),this._cellTextColors$=new S.BehaviorSubject(Array.from(n.values())),this.cellTextColors$=this._cellTextColors$.asObservable()}static async fromFilterColumn(e,r,s){var w,R,x;const n=e.get(u.IUniverInstanceService),{unitId:a,subUnitId:i}=r,o=n.getUniverSheetInstance(a);if(!o)throw new Error(`[ByColorsModel]: Workbook not found for filter model with unitId: ${a}!`);const l=o==null?void 0:o.getSheetBySheetId(i);if(!l)throw new Error(`[ByColorsModel]: Worksheet not found for filter model with unitId: ${a} and subUnitId: ${i}!`);const h=r.getRange(),d=s,p=(w=r.getFilterColumn(s))==null?void 0:w.getColumnData().colorFilters,E=r.getFilteredOutRowsExceptCol(s),_={...h,startRow:h.startRow+1,startColumn:d,endColumn:d},g=new Map,F=new Set((R=p==null?void 0:p.cellFillColors)!=null?R:[]),y=new Map,N=new Set((x=p==null?void 0:p.cellTextColors)!=null?x:[]);for(const $ of l.iterateByColumn(_,!1,!0)){const{row:q,col:J,value:ee}=$;if(E.has(q))continue;const H=l.getComposedCellStyleByCellData(q,J,ee);if(H.bg&&H.bg.rgb){const U=new u.ColorKit(H.bg.rgb).toRgbString();g.has(U)||g.set(U,{color:U,checked:F.has(U)})}else g.set("default-fill-color",{color:null,checked:F.has(null)});if(H.cl&&H.cl.rgb){const U=new u.ColorKit(H.cl.rgb).toRgbString();y.has(U)||y.set(U,{color:U,checked:N.has(U)})}else y.set("default-font-color",{color:z.COLOR_BLACK_RGB,checked:N.has(z.COLOR_BLACK_RGB)})}return e.createInstance(ge,r,s,g,y)}get cellFillColors(){return this._cellFillColors$.getValue()}get cellTextColors(){return this._cellTextColors$.getValue()}dispose(){super.dispose(),this._cellFillColors$.complete()}deltaCol(e){this.col+=e}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}onFilterCheckToggled(e,r=!0){const s=r?this.cellFillColors:this.cellTextColors,n=[];let a=!1;for(let i=0;i<s.length;i++){const o=s[i];if(o.color===e.color){a=!0,n.push({color:o.color,checked:!o.checked});continue}n.push({color:o.color,checked:o.checked})}a&&(this._resetColorsCheckedStatus(!r),r?this._cellFillColors$.next([...n]):this._cellTextColors$.next([...n]))}_resetColorsCheckedStatus(e=!0){const r=e?this.cellFillColors:this.cellTextColors,s=[];for(let n=0;n<r.length;n++)s.push({color:r[n].color,checked:!1});e?this._cellFillColors$.next([...s]):this._cellTextColors$.next([...s])}async apply(){if(this._disposed)return!1;const e=this.cellFillColors.filter(n=>n.checked).map(n=>n.color),r=this.cellTextColors.filter(n=>n.checked).map(n=>n.color);if(e.length===0&&r.length===0)return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null});const s={colId:this.col};return e.length>0?s.colorFilters={cellFillColors:e}:r.length>0&&(s.colorFilters={cellTextColors:r}),this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:s})}};ge=Te([ce(4,u.ICommandService)],ge);const ne="FILTER_PANEL_OPENED",ue={id:"sheet.operation.open-filter-panel",type:u.CommandType.OPERATION,handler:(t,e)=>{const r=t.get(u.IContextService),s=t.get(c.SheetsFilterService),n=t.get(Y);t.get(u.ICommandService).syncExecuteCommand(M.SetCellEditVisibleOperation.id,{visible:!1});const{unitId:i,subUnitId:o,col:l}=e,h=s.getFilterModel(i,o);return h?(n.setupCol(h,l),r.getContextValue(ne)||r.setContextValue(ne,!0),!0):!1}},se={id:"sheet.operation.close-filter-panel",type:u.CommandType.OPERATION,handler:t=>{const e=t.get(u.IContextService),r=t.get(Y),s=t.get(T.ILayoutService,u.Quantity.OPTIONAL);return e.getContextValue(ne)?(e.setContextValue(ne,!1),s==null||s.focus(),r.terminate()):!1}},Me={id:"sheet.operation.apply-filter",type:u.CommandType.OPERATION,handler:(t,e)=>{const{filterBy:r}=e;return t.get(Y).changeFilterBy(r)}},qe="sheets-filter-ui.config",Se={};var mt=Object.getOwnPropertyDescriptor,pt=(t,e,r,s)=>{for(var n=s>1?void 0:s?mt(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},ie=(t,e)=>(r,s)=>e(r,s,t);let oe=class extends u.Disposable{constructor(t,e,r,s,n,a){super(),this._sheetsFilterService=t,this._localeService=e,this._commandService=r,this._sheetPermissionCheckPermission=s,this._injector=n,this._sheetsSelectionService=a,this._commandExecutedListener()}_commandExecutedListener(){this.disposeWithMe(this._commandService.beforeCommandExecuted(t=>{var e,r,s;if(t.id===c.SmartToggleSheetsFilterCommand.id){const n=this._injector.get(u.IUniverInstanceService),a=I.getSheetCommandTarget(n);if(!a)return;const{unitId:i,subUnitId:o,worksheet:l}=a,h=(e=this._sheetsFilterService.getFilterModel(i,o))==null?void 0:e.getRange();let d;if(h)d=this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[I.RangeProtectionPermissionViewPoint],worksheetTypes:[I.WorksheetFilterPermission,I.WorksheetViewPermission]},[h]);else{const p=(r=this._sheetsSelectionService.getCurrentLastSelection())==null?void 0:r.range;if(p){let E={...p};E=p.startColumn===p.endColumn&&p.startRow===p.endRow?I.expandToContinuousRange(E,{left:!0,right:!0,up:!0,down:!0},l):E,d=this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[I.RangeProtectionPermissionViewPoint],worksheetTypes:[I.WorksheetViewPermission,I.WorksheetFilterPermission]},[E],i,o)}else d=this._sheetPermissionCheckPermission.permissionCheckWithoutRange({rangeTypes:[I.RangeProtectionPermissionViewPoint],worksheetTypes:[I.WorksheetViewPermission,I.WorksheetFilterPermission]})}d||this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr"))}if(t.id===ue.id){const n=t.params,{unitId:a,subUnitId:i}=n,o=(s=this._sheetsFilterService.getFilterModel(a,i))==null?void 0:s.getRange(),l=u.Tools.deepClone(o);l&&(l.startColumn=n.col,l.endColumn=n.col,this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[I.RangeProtectionPermissionViewPoint],worksheetTypes:[I.WorksheetFilterPermission,I.WorksheetViewPermission]},[l])||this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr")))}}))}};oe=pt([ie(0,u.Inject(c.SheetsFilterService)),ie(1,u.Inject(u.LocaleService)),ie(2,u.ICommandService),ie(3,u.Inject(I.SheetPermissionCheckController)),ie(4,u.Inject(u.Injector)),ie(5,u.Inject(I.SheetsSelectionsService))],oe);const G=16,ke=new Path2D("M4 6L8 10L12 6Z");class je{static drawNoCriteria(e,r,s,n){e.save(),z.Rect.drawWith(e,{radius:2,width:G,height:G,fill:n}),e.scale(r/G,r/G),e.fillStyle=s,e.fill(ke),e.restore()}static drawHasCriteria(e,r,s,n){e.save(),z.Rect.drawWith(e,{radius:2,width:G,height:G,fill:n}),e.scale(r/G,r/G),e.fillStyle=s,e.fill(ke),e.restore()}}var ft=Object.getOwnPropertyDescriptor,vt=(t,e,r,s)=>{for(var n=s>1?void 0:s?ft(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},ye=(t,e)=>(r,s)=>e(r,s,t);const V=16,Ne=1;let we=class extends z.Shape{constructor(e,r,s,n,a){super(e,r);v(this,"_cellWidth",0);v(this,"_cellHeight",0);v(this,"_filterParams");v(this,"_hovered",!1);this._contextService=s,this._commandService=n,this._themeService=a,this.setShapeProps(r),this.onPointerDown$.subscribeEvent(i=>this.onPointerDown(i)),this.onPointerEnter$.subscribeEvent(()=>this.onPointerEnter()),this.onPointerLeave$.subscribeEvent(()=>this.onPointerLeave())}setShapeProps(e){typeof e.cellHeight<"u"&&(this._cellHeight=e.cellHeight),typeof e.cellWidth<"u"&&(this._cellWidth=e.cellWidth),typeof e.filterParams<"u"&&(this._filterParams=e.filterParams),this.transformByState({width:e.width,height:e.height})}_draw(e){const r=this._cellHeight,s=this._cellWidth,n=V-s,a=V-r;e.save();const i=new Path2D;i.rect(n,a,s,r),e.clip(i);const{hasCriteria:o}=this._filterParams,l=o?this._themeService.getColorFromTheme("primary.600"):this._themeService.getColorFromTheme("black"),h=this._hovered?this._themeService.getColorFromTheme("gray.50"):"rgba(255, 255, 255, 1.0)";o?je.drawHasCriteria(e,V,l,h):je.drawNoCriteria(e,V,l,h),e.restore()}onPointerDown(e){if(e.button===2)return;const{col:r,unitId:s,subUnitId:n}=this._filterParams;this._contextService.getContextValue(ne)||!this._commandService.hasCommand(ue.id)||setTimeout(()=>{this._commandService.executeCommand(ue.id,{unitId:s,subUnitId:n,col:r})},200)}onPointerEnter(){this._hovered=!0,this.makeDirty(!0)}onPointerLeave(){this._hovered=!1,this.makeDirty(!0)}};we=vt([ye(2,u.IContextService),ye(3,u.ICommandService),ye(4,u.Inject(u.ThemeService))],we);var _t=Object.getOwnPropertyDescriptor,gt=(t,e,r,s)=>{for(var n=s>1?void 0:s?_t(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},X=(t,e)=>(r,s)=>e(r,s,t);const St=1e3,Ct=5e3;let xe=class extends u.RxDisposable{constructor(e,r,s,n,a,i,o,l){super();v(this,"_filterRangeShape",null);v(this,"_buttonRenderDisposable",null);v(this,"_filterButtonShapes",[]);this._context=e,this._injector=r,this._sheetSkeletonManagerService=s,this._sheetsFilterService=n,this._themeService=a,this._sheetInterceptorService=i,this._commandService=o,this._selectionRenderService=l,this._initRenderer()}dispose(){super.dispose(),this._disposeRendering()}_initRenderer(){this._sheetSkeletonManagerService.currentSkeleton$.pipe(S.switchMap(e=>{var o,l;if(!e)return S.of(null);const{unit:r,unitId:s}=this._context,n=((o=r.getActiveSheet())==null?void 0:o.getSheetId())||"",a=(l=this._sheetsFilterService.getFilterModel(s,n))!=null?l:void 0,i=()=>({unitId:s,worksheetId:n,filterModel:a,range:a==null?void 0:a.getRange(),skeleton:e.skeleton});return u.fromCallback(this._commandService.onCommandExecuted.bind(this._commandService)).pipe(S.filter(([h])=>{var d;return h.type===u.CommandType.MUTATION&&((d=h.params)==null?void 0:d.unitId)===r.getUnitId()&&(c.FILTER_MUTATIONS.has(h.id)||h.id===I.SetRangeValuesMutation.id)}),S.throttleTime(20,void 0,{leading:!1,trailing:!0}),S.map(i),S.startWith(i()))}),S.takeUntil(this.dispose$)).subscribe(e=>{this._disposeRendering(),!(!e||!e.range)&&(this._renderRange(e.range,e.skeleton),this._renderButtons(e))})}_renderRange(e,r){const{scene:s}=this._context,{rowHeaderWidth:n,columnHeaderHeight:a}=r,i=this._filterRangeShape=new M.SelectionControl(s,St,this._themeService,{rowHeaderWidth:n,columnHeaderHeight:a,enableAutoFill:!1,highlightHeader:!1}),o={range:e,primary:null,style:{fill:"rgba(0, 0, 0, 0.0)"}},l=M.attachSelectionWithCoord(o,r);i.updateRangeBySelectionWithCoord(l),i.setEvent(!1),s.makeDirty(!0)}_renderButtons(e){const{range:r,filterModel:s,unitId:n,skeleton:a,worksheetId:i}=e,{scene:o}=this._context;this._interceptCellContent(n,i,e.range);const{startColumn:l,endColumn:h,startRow:d}=r;for(let p=l;p<=h;p++){const E=`sheets-filter-button-${p}`,_=M.getCoordByCell(d,p,o,a),{startX:g,startY:F,endX:y,endY:N}=_,w=y-g,R=N-F;if(R<=Ne||w<=Ne)continue;const x=!!s.getFilterColumn(p),$=y-V-Ne,q=F+(R-V)/2,J={left:$,top:q,height:V,width:V,zIndex:Ct,cellHeight:R,cellWidth:w,filterParams:{unitId:n,subUnitId:i,col:p,hasCriteria:x}},ee=this._injector.createInstance(we,E,J);this._filterButtonShapes.push(ee)}o.addObjects(this._filterButtonShapes),o.makeDirty()}_interceptCellContent(e,r,s){const{startRow:n,startColumn:a,endColumn:i}=s;this._buttonRenderDisposable=this._sheetInterceptorService.intercept(I.INTERCEPTOR_POINT.CELL_CONTENT,{effect:u.InterceptorEffectEnum.Style,handler:(o,l,h)=>{const{row:d,col:p,unitId:E,subUnitId:_}=l;return E!==e||_!==r||d!==n||p<a||p>i||((!o||o===l.rawData)&&(o={...l.rawData}),o.fontRenderExtension={...o==null?void 0:o.fontRenderExtension,rightOffset:V}),h(o)},priority:10})}_disposeRendering(){var e,r;(e=this._filterRangeShape)==null||e.dispose(),this._filterButtonShapes.forEach(s=>s.dispose()),(r=this._buttonRenderDisposable)==null||r.dispose(),this._filterRangeShape=null,this._buttonRenderDisposable=null,this._filterButtonShapes=[]}};xe=gt([X(1,u.Inject(u.Injector)),X(2,u.Inject(M.SheetSkeletonManagerService)),X(3,u.Inject(c.SheetsFilterService)),X(4,u.Inject(u.ThemeService)),X(5,u.Inject(I.SheetInterceptorService)),X(6,u.ICommandService),X(7,M.ISheetSelectionRenderService)],xe);var Ft=Object.getOwnPropertyDescriptor,Tt=(t,e,r,s)=>{for(var n=s>1?void 0:s?Ft(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},Be=(t,e)=>(r,s)=>e(r,s,t);let he=class extends u.RxDisposable{constructor(t,e){super(),this._renderManagerService=t,this._sheetsRenderService=e,[c.SetSheetsFilterRangeMutation,c.SetSheetsFilterCriteriaMutation,c.RemoveSheetsFilterMutation,c.ReCalcSheetsFilterMutation].forEach(r=>this.disposeWithMe(this._sheetsRenderService.registerSkeletonChangingMutations(r.id))),this.disposeWithMe(this._renderManagerService.registerRenderModule(u.UniverInstanceType.UNIVER_SHEET,[xe]))}};he=Tt([Be(0,z.IRenderManagerService),Be(1,u.Inject(M.SheetsRenderService))],he);var Et=Object.defineProperty,Ot=Object.getOwnPropertyDescriptor,It=(t,e,r)=>e in t?Et(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,yt=(t,e,r,s)=>{for(var n=s>1?void 0:s?Ot(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},De=(t,e)=>(r,s)=>e(r,s,t),Ke=(t,e,r)=>It(t,typeof e!="symbol"?e+"":e,r);const Nt="SHEET_FILTER_UI_PLUGIN";exports.UniverSheetsFilterMobileUIPlugin=class extends u.Plugin{constructor(e=Se,r,s){super(),this._config=e,this._injector=r,this._configService=s;const{menu:n,...a}=u.merge({},Se,this._config);n&&this._configService.setConfig("menu",n,{merge:!0}),this._configService.setConfig(qe,a)}onStarting(){[[oe],[he]].forEach(e=>this._injector.add(e))}onReady(){this._injector.get(oe)}onRendered(){this._injector.get(he)}};Ke(exports.UniverSheetsFilterMobileUIPlugin,"type",u.UniverInstanceType.UNIVER_SHEET);Ke(exports.UniverSheetsFilterMobileUIPlugin,"pluginName",Nt);exports.UniverSheetsFilterMobileUIPlugin=yt([u.DependentOn(c.UniverSheetsFilterPlugin),De(1,u.Inject(u.Injector)),De(2,u.IConfigService)],exports.UniverSheetsFilterMobileUIPlugin);var j=function(){return j=Object.assign||function(t){for(var e,r=1,s=arguments.length;r<s;r++){e=arguments[r];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},j.apply(this,arguments)},bt=function(t,e){var r={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(r[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(r[s[n]]=t[s[n]]);return r},de=O.forwardRef(function(t,e){var r=t.icon,s=t.id,n=t.className,a=t.extend,i=bt(t,["icon","id","className","extend"]),o="univerjs-icon univerjs-icon-".concat(s," ").concat(n||"").trim(),l=O.useRef("_".concat(At()));return Ze(r,"".concat(s),{defIds:r.defIds,idSuffix:l.current},j({ref:e,className:o},i),a)});function Ze(t,e,r,s,n){return O.createElement(t.tag,j(j({key:e},Rt(t,r,n)),s),(Pt(t,r).children||[]).map(function(a,i){return Ze(a,"".concat(e,"-").concat(t.tag,"-").concat(i),r,void 0,n)}))}function Rt(t,e,r){var s=j({},t.attrs);r!=null&&r.colorChannel1&&s.fill==="colorChannel1"&&(s.fill=r.colorChannel1),t.tag==="mask"&&s.id&&(s.id=s.id+e.idSuffix),Object.entries(s).forEach(function(a){var i=a[0],o=a[1];i==="mask"&&typeof o=="string"&&(s[i]=o.replace(/url\(#(.*)\)/,"url(#$1".concat(e.idSuffix,")")))});var n=e.defIds;return!n||n.length===0||(t.tag==="use"&&s["xlink:href"]&&(s["xlink:href"]=s["xlink:href"]+e.idSuffix),Object.entries(s).forEach(function(a){var i=a[0],o=a[1];typeof o=="string"&&(s[i]=o.replace(/url\(#(.*)\)/,"url(#$1".concat(e.idSuffix,")")))})),s}function Pt(t,e){var r,s=e.defIds;return!s||s.length===0?t:t.tag==="defs"&&(!((r=t.children)===null||r===void 0)&&r.length)?j(j({},t),{children:t.children.map(function(n){return typeof n.attrs.id=="string"&&s&&s.includes(n.attrs.id)?j(j({},n),{attrs:j(j({},n.attrs),{id:n.attrs.id+e.idSuffix})}):n})}):t}function At(){return Math.random().toString(36).substring(2,8)}de.displayName="UniverIcon";var wt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10 1.05957C10.356 1.05957 10.6816 1.26162 10.8408 1.58008L18.8408 17.5801L18.8799 17.668C19.0486 18.1134 18.8551 18.6232 18.4199 18.8408C17.9557 19.0727 17.3913 18.8841 17.1592 18.4199L10 4.10156L2.84082 18.4199C2.60871 18.8841 2.04434 19.0727 1.58008 18.8408C1.11587 18.6087 0.92731 18.0443 1.15918 17.5801L9.15918 1.58008C9.31841 1.26162 9.64395 1.05957 10 1.05957Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M15.3337 11.7261L15.4294 11.731C15.9035 11.779 16.2732 12.1798 16.2732 12.6665C16.2732 13.1532 15.9035 13.554 15.4294 13.602L15.3337 13.6069H4.66675C4.1476 13.6069 3.72632 13.1856 3.72632 12.6665C3.72632 12.1474 4.1476 11.7261 4.66675 11.7261H15.3337Z"}}]},Xe=O.forwardRef(function(t,e){return O.createElement(de,Object.assign({},t,{id:"a-icon",ref:e,icon:wt}))});Xe.displayName="AIcon";var xt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.0596 10C17.0596 6.10087 13.8992 2.94043 10 2.94043C6.10087 2.94043 2.94043 6.10087 2.94043 10C2.94043 13.8992 6.10087 17.0596 10 17.0596C13.8992 17.0596 17.0596 13.8992 17.0596 10ZM18.9404 10C18.9404 14.9374 14.9374 18.9404 10 18.9404C5.06257 18.9404 1.05957 14.9374 1.05957 10C1.05957 5.06257 5.06257 1.05957 10 1.05957C14.9374 1.05957 18.9404 5.06257 18.9404 10Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M4.29492 4.13476C4.63911 3.79057 5.1845 3.76906 5.55371 4.07031L5.625 4.13476L16.0244 14.5352L16.0889 14.6064C16.3902 14.9757 16.3686 15.52 16.0244 15.8643C15.6573 16.2313 15.0624 16.2313 14.6953 15.8643L4.29492 5.46484L4.23047 5.39355C3.92922 5.02434 3.95073 4.47895 4.29492 4.13476Z"}}]},ze=O.forwardRef(function(t,e){return O.createElement(de,Object.assign({},t,{id:"ban-icon",ref:e,icon:xt}))});ze.displayName="BanIcon";var Lt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.32182 2.60967C2.98161 2.60967 2.79671 3.0074 3.01601 3.2675L6.85819 7.8246C6.94943 7.93282 6.99947 8.06981 6.99947 8.21136V12.7338C6.99947 12.898 7.0998 13.0455 7.2525 13.1058L8.73833 13.6928C9.00085 13.7965 9.28531 13.6031 9.28531 13.3208V8.21136C9.28531 8.06981 9.33535 7.93282 9.42659 7.8246L13.2688 3.2675C13.4881 3.0074 13.3032 2.60967 12.963 2.60967H3.32182ZM2.09858 4.04101C1.22139 3.0006 1.96097 1.40967 3.32182 1.40967H12.963C14.3238 1.40967 15.0634 3.0006 14.1862 4.04101L10.4853 8.43054V13.3208C10.4853 14.4498 9.34747 15.2237 8.29742 14.8089L6.81158 14.2219C6.20078 13.9806 5.79947 13.3905 5.79947 12.7338V8.43054L2.09858 4.04101Z",fillRule:"evenodd",clipRule:"evenodd"}}]},Je=O.forwardRef(function(t,e){return O.createElement(de,Object.assign({},t,{id:"filter-icon",ref:e,icon:Lt}))});Je.displayName="FilterIcon";var Mt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM11.7245 6.42417C11.9588 6.18985 11.9588 5.80995 11.7245 5.57564C11.4901 5.34132 11.1102 5.34132 10.8759 5.57564L7.3002 9.15137L5.72446 7.57564C5.49014 7.34132 5.11025 7.34132 4.87593 7.57564C4.64162 7.80995 4.64162 8.18985 4.87593 8.42417L6.87593 10.4242C7.11025 10.6585 7.49014 10.6585 7.72446 10.4242L11.7245 6.42417Z",fillRule:"evenodd",clipRule:"evenodd"}}]},et=O.forwardRef(function(t,e){return O.createElement(de,Object.assign({},t,{id:"success-icon",ref:e,icon:Mt}))});et.displayName="SuccessIcon";function $t(t){const{model:e}=t,r=T.useDependency(u.LocaleService),s=T.useObservable(e.cellFillColors$,[],!0),n=T.useObservable(e.cellTextColors$,[],!0),a=O.useCallback(o=>{e.onFilterCheckToggled(o)},[e]),i=O.useCallback(o=>{e.onFilterCheckToggled(o,!1)},[e]);return m.jsx("div",{"data-u-comp":"sheets-filter-panel-colors-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:m.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:b.clsx("univer-mt-2 univer-box-border univer-flex univer-h-[300px] univer-flex-grow univer-flex-col univer-gap-4 univer-overflow-auto univer-rounded-md univer-px-2 univer-py-2.5",b.borderClassName),children:[s.length>1&&m.jsxs("div",{children:[m.jsx("div",{className:"univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",children:r.t("sheets-filter.panel.filter-by-cell-fill-color")}),m.jsx("div",{className:"univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",children:s.map((o,l)=>m.jsxs("div",{className:"univer-relative univer-h-6 univer-w-6",onClick:()=>a(o),children:[o.color?m.jsx("button",{type:"button",className:b.clsx("univer-box-border univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full univer-border univer-border-solid univer-border-transparent univer-bg-gray-300 univer-transition-shadow hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"),style:{backgroundColor:o.color}}):m.jsx(ze,{className:"univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"}),o.checked&&m.jsx(He,{})]},`sheets-filter-cell-fill-color-${l}`))})]}),n.length>1&&m.jsxs("div",{children:[m.jsx("div",{className:"univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",children:r.t("sheets-filter.panel.filter-by-cell-text-color")}),m.jsx("div",{className:"univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",children:n.map((o,l)=>m.jsxs("div",{className:"univer-relative univer-h-6 univer-w-6",onClick:()=>i(o),children:[m.jsx("div",{className:"univer-box-border univer-flex univer-h-full univer-w-full univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-border univer-border-solid univer-border-[rgba(13,13,13,0.06)] univer-p-0.5 hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white dark:!univer-border-[rgba(255,255,255,0.06)]",children:m.jsx(Xe,{style:{color:o.color}})}),o.checked&&m.jsx(He,{})]},`sheets-filter-cell-text-color-${l}`))})]}),s.length<=1&&n.length<=1&&m.jsx("div",{className:"univer-flex univer-h-full univer-w-full univer-items-center univer-justify-center univer-text-sm univer-text-gray-900 dark:!univer-text-gray-200",children:r.t("sheets-filter.panel.filter-by-color-none")})]})})}function He(){return m.jsx("div",{className:"univer-absolute -univer-bottom-0.5 -univer-right-0.5 univer-flex univer-h-3 univer-w-3 univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-bg-white",children:m.jsx(et,{className:"univer-h-full univer-w-full univer-font-bold univer-text-[#418F1F]"})})}function Ut(t){var _,g;const{model:e}=t,r=T.useDependency(u.LocaleService),s=T.useObservable(e.conditionItem$,void 0),n=T.useObservable(e.filterConditionFormParams$,void 0),a=n!=null&&n.and?"AND":"OR",i=O.useCallback(F=>{e.onConditionFormChange({and:F==="AND"})},[e]),o=kt(r),l=O.useCallback(F=>{e.onPrimaryConditionChange(F)},[e]),h=jt(r),d=O.useCallback(F=>{e.onConditionFormChange(F)},[e]),p=r.t("sheets-filter.panel.input-values-placeholder");function E(F,y,N){const w=f.getItemByOperator(F).numOfParameters===1;return m.jsxs(m.Fragment,{children:[N==="operator2"&&m.jsxs(b.RadioGroup,{value:a,onChange:i,children:[m.jsx(b.Radio,{value:"AND",children:r.t("sheets-filter.panel.and")}),m.jsx(b.Radio,{value:"OR",children:r.t("sheets-filter.panel.or")})]}),m.jsx(b.Select,{value:F,options:h,onChange:R=>d({[N]:R})}),w&&m.jsx("div",{children:m.jsx(b.Input,{className:"univer-mt-2",value:y,placeholder:p,onChange:R=>d({[N==="operator1"?"val1":"val2"]:R})})})]})}return m.jsx("div",{"data-u-comp":"sheets-filter-panel-conditions-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:s&&n&&m.jsxs(m.Fragment,{children:[m.jsx(b.Select,{value:s.operator,options:o,onChange:l}),f.getItemByOperator(s.operator).numOfParameters!==0?m.jsxs("div",{"data-u-comp":"sheets-filter-panel-conditions-container-inner",className:b.clsx("univer-mt-2 univer-flex-grow univer-overflow-hidden univer-rounded-md univer-p-2",b.borderClassName),children:[s.numOfParameters>=1&&E(n.operator1,(_=n.val1)!=null?_:"","operator1"),s.numOfParameters>=2&&E(n.operator2,(g=n.val2)!=null?g:"","operator2"),m.jsxs("div",{"data-u-comp":"sheets-filter-panel-conditions-desc",className:"univer-mt-2 univer-text-xs univer-text-gray-500",children:[r.t("sheets-filter.panel.?"),m.jsx("br",{}),r.t("sheets-filter.panel.*")]})]}):null]})})}function kt(t){const e=t.getCurrentLocale();return O.useMemo(()=>[{options:[{label:t.t(f.NONE.label),value:f.NONE.operator}]},{options:[{label:t.t(f.EMPTY.label),value:f.EMPTY.operator},{label:t.t(f.NOT_EMPTY.label),value:f.NOT_EMPTY.operator}]},{options:[{label:t.t(f.TEXT_CONTAINS.label),value:f.TEXT_CONTAINS.operator},{label:t.t(f.DOES_NOT_CONTAIN.label),value:f.DOES_NOT_CONTAIN.operator},{label:t.t(f.STARTS_WITH.label),value:f.STARTS_WITH.operator},{label:t.t(f.ENDS_WITH.label),value:f.ENDS_WITH.operator},{label:t.t(f.EQUALS.label),value:f.EQUALS.operator}]},{options:[{label:t.t(f.GREATER_THAN.label),value:f.GREATER_THAN.operator},{label:t.t(f.GREATER_THAN_OR_EQUAL.label),value:f.GREATER_THAN_OR_EQUAL.operator},{label:t.t(f.LESS_THAN.label),value:f.LESS_THAN.operator},{label:t.t(f.LESS_THAN_OR_EQUAL.label),value:f.LESS_THAN_OR_EQUAL.operator},{label:t.t(f.EQUAL.label),value:f.EQUAL.operator},{label:t.t(f.NOT_EQUAL.label),value:f.NOT_EQUAL.operator},{label:t.t(f.BETWEEN.label),value:f.BETWEEN.operator},{label:t.t(f.NOT_BETWEEN.label),value:f.NOT_BETWEEN.operator}]},{options:[{label:t.t(f.CUSTOM.label),value:f.CUSTOM.operator}]}],[e,t])}function jt(t){const e=t.getCurrentLocale();return O.useMemo(()=>f.ALL_CONDITIONS.filter(r=>r.numOfParameters!==2).map(r=>({label:t.t(r.label),value:r.operator})),[e,t])}function Bt(t){const{model:e}=t,r=T.useDependency(u.LocaleService),s=T.useObservable(e.searchString$,"",!0),n=T.useObservable(e.filterItems$,void 0,!0),a=r.t("sheets-filter.panel.filter-only"),i=Re(n),o=i.checked>0&&i.unchecked===0,l=i.checked>0&&i.unchecked>0,h=e.treeMapCache,d=O.useCallback(()=>{e.onCheckAllToggled(!o)},[e,o]),p=O.useCallback(_=>{e.setSearchString(_)},[e]);function E(_){let g=[];return _.forEach(F=>{F.checked&&g.push(F.key),F.children&&(g=g.concat(E(F.children)))}),g}return m.jsxs("div",{"data-u-comp":"sheets-filter-panel-values-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:[m.jsx(b.Input,{autoFocus:!0,value:s,placeholder:r.t("sheets-filter.panel.search-placeholder"),onChange:p}),m.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:b.clsx("univer-mt-2 univer-box-border univer-flex univer-flex-grow univer-flex-col univer-overflow-hidden univer-rounded-md univer-px-2 univer-py-2.5",b.borderClassName),children:[m.jsx("div",{"data-u-comp":"sheets-filter-panel-values-item",className:"univer-box-border univer-h-8 univer-w-full univer-py-0.5",children:m.jsxs("div",{"data-u-comp":"sheets-filter-panel-values-item-inner",className:"univer-box-border univer-flex univer-h-7 univer-items-center univer-rounded-md univer-pb-0 univer-pl-5 univer-pr-0.5 univer-pt-0 univer-text-sm",children:[m.jsx(b.Checkbox,{indeterminate:l,disabled:n.length===0,checked:o,onChange:d}),m.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-text",className:"univer-mx-1 univer-inline-block univer-flex-shrink univer-overflow-hidden univer-text-ellipsis univer-whitespace-nowrap univer-text-gray-900 dark:!univer-text-white",children:`${r.t("sheets-filter.panel.select-all")}`}),m.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-count",className:"univer-text-gray-400 dark:!univer-text-gray-500",children:`(${i.checked}/${i.checked+i.unchecked})`})]})}),m.jsx("div",{"data-u-comp":"sheets-filter-panel-values-virtual",className:"univer-flex-grow",children:m.jsx(b.Tree,{data:n,defaultExpandAll:!1,valueGroup:E(n),onChange:_=>{e.onFilterCheckToggled(_)},defaultCache:h,itemHeight:28,treeNodeClassName:`
                          univer-pr-2 univer-border-box univer-max-w-[245px] univer-rounded-md
                          [&:hover_a]:univer-inline-block
                          hover:univer-bg-gray-50 univer-h-full
                          univer-text-gray-900 dark:hover:!univer-bg-gray-900
                          dark:!univer-text-white
                        `,attachRender:_=>m.jsxs("div",{className:"univer-ml-1 univer-flex univer-h-5 univer-flex-1 univer-cursor-pointer univer-items-center univer-justify-between univer-text-sm univer-text-primary-500",children:[m.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-count",className:"univer-text-gray-400 dark:!univer-text-gray-500",children:`(${_.count})`}),m.jsx("a",{className:"univer-box-border univer-hidden univer-h-4 univer-whitespace-nowrap univer-px-1.5",onClick:()=>{const g=[];_.children?_.children.forEach(F=>{F.children?F.children.forEach(y=>{g.push(y.key)}):g.push(F.key)}):g.push(_.key),e.onFilterOnly(g)},children:a})]})})})]})]})}function Dt(){var y;const t=T.useDependency(Y),e=T.useDependency(u.LocaleService),r=T.useDependency(u.ICommandService),s=T.useObservable(t.filterBy$,void 0,!0),n=T.useObservable(t.filterByModel$,void 0,!1),a=T.useObservable(()=>(n==null?void 0:n.canApply$)||S.of(!1),void 0,!1,[n]),i=Ht(e),o=!T.useObservable(t.hasCriteria$),l=O.useCallback(N=>{r.executeCommand(Me.id,{filterBy:N})},[r]),h=O.useCallback(async()=>{await(n==null?void 0:n.clear()),r.executeCommand(se.id)},[n,r]),d=O.useCallback(()=>{r.executeCommand(se.id)},[r]),p=O.useCallback(async()=>{await(n==null?void 0:n.apply()),r.executeCommand(se.id)},[n,r]),_=(y=T.useDependency(c.SheetsFilterService).activeFilterModel)==null?void 0:y.getRange(),g=t.col,F=T.useComponentsOfPart(M.SheetsUIPart.FILTER_PANEL_EMBED_POINT);return m.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:"univer-box-border univer-flex univer-max-h-[500px] univer-w-[400px] univer-flex-col univer-rounded-lg univer-bg-white univer-p-4 univer-shadow-lg dark:!univer-border-gray-600 dark:!univer-bg-gray-700",children:[m.jsx(T.ComponentContainer,{components:F,sharedProps:{range:_,colIndex:g,onClose:d}}),m.jsx("div",{className:"univer-mb-1 univer-flex-shrink-0 univer-flex-grow-0",children:m.jsx(b.Segmented,{value:s,items:i,onChange:N=>l(N)})}),n?m.jsx("div",{"data-u-comp":"sheets-filter-panel-content",className:"univer-flex-shrink univer-flex-grow univer-pt-2",children:s===c.FilterBy.VALUES?m.jsx(Bt,{model:n}):s===c.FilterBy.COLORS?m.jsx($t,{model:n}):m.jsx(Ut,{model:n})}):m.jsx("div",{className:"univer-flex-1"}),m.jsxs("div",{"data-u-comp":"sheets-filter-panel-footer",className:"univer-mt-4 univer-inline-flex univer-flex-shrink-0 univer-flex-grow-0 univer-flex-nowrap univer-justify-between univer-overflow-hidden",children:[m.jsx(b.Button,{variant:"link",onClick:h,disabled:o,children:e.t("sheets-filter.panel.clear-filter")}),m.jsxs("span",{className:"univer-flex univer-gap-2",children:[m.jsx(b.Button,{variant:"default",onClick:d,children:e.t("sheets-filter.panel.cancel")}),m.jsx(b.Button,{disabled:!a,variant:"primary",onClick:p,children:e.t("sheets-filter.panel.confirm")})]})]})]})}function Ht(t){const e=t.getCurrentLocale();return O.useMemo(()=>[{label:t.t("sheets-filter.panel.by-values"),value:c.FilterBy.VALUES},{label:t.t("sheets-filter.panel.by-colors"),value:c.FilterBy.COLORS},{label:t.t("sheets-filter.panel.by-conditions"),value:c.FilterBy.CONDITIONS}],[e,t])}function Wt(t){const e=t.get(c.SheetsFilterService);return{id:c.SmartToggleSheetsFilterCommand.id,type:T.MenuItemType.BUTTON_SELECTOR,icon:"FilterIcon",tooltip:"sheets-filter.toolbar.smart-toggle-filter-tooltip",hidden$:T.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),activated$:e.activeFilterModel$.pipe(S.map(r=>!!r)),disabled$:M.getObservableWithExclusiveRange$(t,M.getCurrentRangeDisable$(t,{worksheetTypes:[I.WorksheetFilterPermission,I.WorksheetViewPermission],rangeTypes:[I.RangeProtectionPermissionViewPoint]}))}}function Vt(t){const e=t.get(c.SheetsFilterService);return{id:c.ClearSheetsFilterCriteriaCommand.id,type:T.MenuItemType.BUTTON,title:"sheets-filter.toolbar.clear-filter-criteria",hidden$:T.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),disabled$:e.activeFilterModel$.pipe(S.switchMap(r=>{var s;return(s=r==null?void 0:r.hasCriteria$.pipe(S.map(n=>!n)))!=null?s:S.of(!0)}))}}function Qt(t){const e=t.get(c.SheetsFilterService);return{id:c.ReCalcSheetsFilterCommand.id,type:T.MenuItemType.BUTTON,title:"sheets-filter.toolbar.re-calc-filter-conditions",hidden$:T.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),disabled$:e.activeFilterModel$.pipe(S.switchMap(r=>{var s;return(s=r==null?void 0:r.hasCriteria$.pipe(S.map(n=>!n)))!=null?s:S.of(!0)}))}}const Gt={[T.RibbonDataGroup.ORGANIZATION]:{[c.SmartToggleSheetsFilterCommand.id]:{order:2,menuItemFactory:Wt,[c.ClearSheetsFilterCriteriaCommand.id]:{order:0,menuItemFactory:Vt},[c.ReCalcSheetsFilterCommand.id]:{order:1,menuItemFactory:Qt}}}},Yt={id:c.SmartToggleSheetsFilterCommand.id,binding:T.KeyCode.L|T.MetaKeys.CTRL_COMMAND|T.MetaKeys.SHIFT,description:"sheets-filter.shortcut.smart-toggle-filter",preconditions:M.whenSheetEditorFocused,group:"4_sheet-edit"};var qt=Object.getOwnPropertyDescriptor,Kt=(t,e,r,s)=>{for(var n=s>1?void 0:s?qt(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},L=(t,e)=>(r,s)=>e(r,s,t);const We="FILTER_PANEL_POPUP";let Ce=class extends he{constructor(e,r,s,n,a,i,o,l,h,d,p,E,_){super(_,E);v(this,"_popupDisposable");this._injector=e,this._componentManager=r,this._sheetsFilterPanelService=s,this._sheetCanvasPopupService=n,this._sheetsFilterService=a,this._localeService=i,this._shortcutService=o,this._commandService=l,this._menuManagerService=h,this._contextService=d,this._messageService=p,this._initCommands(),this._initShortcuts(),this._initMenuItems(),this._initUI()}dispose(){super.dispose(),this._closeFilterPopup()}_initShortcuts(){[Yt].forEach(e=>{this.disposeWithMe(this._shortcutService.registerShortcut(e))})}_initCommands(){[c.SmartToggleSheetsFilterCommand,c.RemoveSheetFilterCommand,c.SetSheetFilterRangeCommand,c.SetSheetsFilterCriteriaCommand,c.ClearSheetsFilterCriteriaCommand,c.ReCalcSheetsFilterCommand,Me,ue,se].forEach(e=>{this.disposeWithMe(this._commandService.registerCommand(e))})}_initMenuItems(){this._menuManagerService.mergeMenu(Gt)}_initUI(){[[We,Dt],["FilterIcon",Je]].forEach(([e,r])=>{this.disposeWithMe(this._componentManager.register(e,r))}),this.disposeWithMe(this._contextService.subscribeContextValue$(ne).pipe(S.distinctUntilChanged()).subscribe(e=>{e?this._openFilterPopup():this._closeFilterPopup()})),this.disposeWithMe(this._sheetsFilterService.errorMsg$.subscribe(e=>{e&&this._messageService.show({type:b.MessageType.Error,content:this._localeService.t(e)})}))}_openFilterPopup(){const e=this._sheetsFilterPanelService.filterModel;if(!e)throw new Error("[SheetsFilterUIController]: no filter model when opening filter popup!");const r=e.getRange(),s=this._sheetsFilterPanelService.col,{startRow:n}=r;this._popupDisposable=this._sheetCanvasPopupService.attachPopupToCell(n,s,{componentKey:We,direction:"horizontal",onClickOutside:()=>this._commandService.syncExecuteCommand(se.id),offset:[5,0]})}_closeFilterPopup(){var e;(e=this._popupDisposable)==null||e.dispose(),this._popupDisposable=null}};Ce=Kt([L(0,u.Inject(u.Injector)),L(1,u.Inject(T.ComponentManager)),L(2,u.Inject(Y)),L(3,u.Inject(M.SheetCanvasPopManagerService)),L(4,u.Inject(c.SheetsFilterService)),L(5,u.Inject(u.LocaleService)),L(6,T.IShortcutService),L(7,u.ICommandService),L(8,T.IMenuManagerService),L(9,u.IContextService),L(10,T.IMessageService),L(11,u.Inject(M.SheetsRenderService)),L(12,z.IRenderManagerService)],Ce);var Zt=Object.defineProperty,Xt=Object.getOwnPropertyDescriptor,zt=(t,e,r)=>e in t?Zt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Jt=(t,e,r,s)=>{for(var n=s>1?void 0:s?Xt(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},be=(t,e)=>(r,s)=>e(r,s,t),tt=(t,e,r)=>zt(t,typeof e!="symbol"?e+"":e,r);const er="SHEET_FILTER_UI_PLUGIN";exports.UniverSheetsFilterUIPlugin=class extends u.Plugin{constructor(e=Se,r,s,n){super(),this._config=e,this._injector=r,this._configService=s,this._rpcChannelService=n;const{menu:a,...i}=u.merge({},Se,this._config);a&&this._configService.setConfig("menu",a,{merge:!0}),this._configService.setConfig(qe,i)}onStarting(){u.registerDependencies(this._injector,[[Y],[oe],[Ce]]),this._config.useRemoteFilterValuesGenerator&&this._rpcChannelService&&this._injector.add([fe,{useFactory:()=>Fe.toModule(this._rpcChannelService.requestChannel(Le))}])}onReady(){u.touchDependencies(this._injector,[[oe]])}onRendered(){u.touchDependencies(this._injector,[[Ce]])}};tt(exports.UniverSheetsFilterUIPlugin,"type",u.UniverInstanceType.UNIVER_SHEET);tt(exports.UniverSheetsFilterUIPlugin,"pluginName",er);exports.UniverSheetsFilterUIPlugin=Jt([u.DependentOn(c.UniverSheetsFilterPlugin),be(1,u.Inject(u.Injector)),be(2,u.IConfigService),be(3,u.Optional(Fe.IRPCChannelService))],exports.UniverSheetsFilterUIPlugin);var tr=Object.getOwnPropertyDescriptor,rr=(t,e,r,s)=>{for(var n=s>1?void 0:s?tr(e,r):e,a=t.length-1,i;a>=0;a--)(i=t[a])&&(n=i(n)||n);return n},Ve=(t,e)=>(r,s)=>e(r,s,t),pe;exports.UniverSheetsFilterUIWorkerPlugin=(pe=class extends u.Plugin{constructor(e,r,s){super(),this._config=e,this._injector=r,this._rpcChannelService=s}onStarting(){[[fe,{useClass:Pe}]].forEach(e=>this._injector.add(e))}onReady(){this._rpcChannelService.registerChannel(Le,Fe.fromModule(this._injector.get(fe)))}},v(pe,"type",u.UniverInstanceType.UNIVER_SHEET),v(pe,"pluginName","SHEET_FILTER_UI_WORKER_PLUGIN"),pe);exports.UniverSheetsFilterUIWorkerPlugin=rr([Ve(1,u.Inject(u.Injector)),Ve(2,Fe.IRPCChannelService)],exports.UniverSheetsFilterUIWorkerPlugin);exports.ChangeFilterByOperation=Me;exports.CloseFilterPanelOperation=se;exports.OpenFilterPanelOperation=ue;

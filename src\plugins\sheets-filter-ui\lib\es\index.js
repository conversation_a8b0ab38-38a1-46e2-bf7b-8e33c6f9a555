var nr = Object.defineProperty;
var or = (t, e, r) => e in t ? nr(t, e, { enumerable: !0, configurable: !0, writable: !0, value: r }) : t[e] = r;
var p = (t, e, r) => or(t, typeof e != "symbol" ? e + "" : e, r);
import { BooleanNumber as ht, createIdentifier as At, Inject as T, LocaleService as K, IUniverInstanceService as He, ILogService as lr, extractPureTextFromCell as ar, numfmt as dt, Disposable as de, Injector as re, Quantity as Pt, Tools as be, ColorKit as mt, ICommandService as j, CommandType as De, IContextService as Be, ThemeService as wt, RxDisposable as Lt, fromCallback as cr, InterceptorEffectEnum as ur, UniverInstanceType as ie, DependentOn as $t, IConfigService as Mt, Plugin as tt, merge as xt, Optional as hr, registerDependencies as dr, touchDependencies as ft } from "@univerjs/core";
import { CustomFilterOperator as u, FilterBy as A, SetSheetsFilterCriteriaCommand as Q, SheetsFilterService as X, SmartToggleSheetsFilterCommand as Ee, FILTER_MUTATIONS as mr, SetSheetsFilterRangeMutation as fr, SetSheetsFilterCriteriaMutation as pr, RemoveSheetsFilterMutation as vr, ReCalcSheetsFilterMutation as _r, UniverSheetsFilterPlugin as Ut, ReCalcSheetsFilterCommand as rt, ClearSheetsFilterCriteriaCommand as it, RemoveSheetFilterCommand as gr, SetSheetFilterRangeCommand as Sr } from "@univerjs/sheets-filter";
import { SetCellEditVisibleOperation as Cr, SheetSkeletonManagerService as Tr, ISheetSelectionRenderService as Er, SelectionControl as Nr, attachSelectionWithCoord as Fr, getCoordByCell as yr, SheetsRenderService as kt, SheetsUIPart as Or, getObservableWithExclusiveRange$ as Ir, getCurrentRangeDisable$ as br, whenSheetEditorFocused as Rr, SheetCanvasPopManagerService as Ar } from "@univerjs/sheets-ui";
import { ILayoutService as Pr, useDependency as te, useObservable as H, useComponentsOfPart as wr, ComponentContainer as Lr, getMenuHiddenObservable as st, MenuItemType as nt, RibbonDataGroup as $r, KeyCode as Mr, MetaKeys as pt, ComponentManager as xr, IShortcutService as Ur, IMenuManagerService as kr, IMessageService as Hr } from "@univerjs/ui";
import { COLOR_BLACK_RGB as vt, Rect as _t, Shape as Dr, IRenderManagerService as Ht } from "@univerjs/engine-render";
import { RefRangeService as Br, SheetPermissionCheckController as Wr, SheetsSelectionsService as Qr, getSheetCommandTarget as Vr, WorksheetFilterPermission as pe, WorksheetViewPermission as ve, RangeProtectionPermissionViewPoint as _e, expandToContinuousRange as jr, SheetInterceptorService as Gr, SetRangeValuesMutation as Yr, INTERCEPTOR_POINT as Zr } from "@univerjs/sheets";
import { BehaviorSubject as V, ReplaySubject as Kr, Subject as Xr, merge as qr, combineLatest as zr, throttleTime as Dt, startWith as Bt, map as ce, shareReplay as Jr, of as me, switchMap as ot, filter as ei, takeUntil as ti, distinctUntilChanged as ri } from "rxjs";
import { IRPCChannelService as Wt, toModule as ii, fromModule as si } from "@univerjs/rpc";
import { clsx as Re, borderClassName as lt, Select as gt, RadioGroup as ni, Radio as St, Input as Qt, Checkbox as oi, Tree as li, Segmented as ai, Button as je, MessageType as ci } from "@univerjs/design";
import { jsx as f, jsxs as I, Fragment as Ct } from "react/jsx-runtime";
import { forwardRef as Ne, useRef as ui, createElement as Fe, useCallback as U, useMemo as at } from "react";
var y = /* @__PURE__ */ ((t) => (t[t.FIRST = 0] = "FIRST", t[t.SECOND = 1] = "SECOND", t))(y || {}), g = /* @__PURE__ */ ((t) => (t.NONE = "none", t.STARTS_WITH = "startsWith", t.DOES_NOT_START_WITH = "doesNotStartWith", t.ENDS_WITH = "endsWith", t.DOES_NOT_END_WITH = "doesNotEndWith", t.CONTAINS = "contains", t.DOES_NOT_CONTAIN = "doesNotContain", t.EQUALS = "equals", t.NOT_EQUALS = "notEquals", t.EMPTY = "empty", t.NOT_EMPTY = "notEmpty", t.BETWEEN = "between", t.NOT_BETWEEN = "notBetween", t.CUSTOM = "custom", t))(g || {}), m;
((t) => {
  t.NONE = {
    label: "sheets-filter.conditions.none",
    operator: g.NONE,
    order: y.SECOND,
    numOfParameters: 0,
    getDefaultFormParams: () => {
      throw new Error("[FilterConditionItems.NONE]: should not have initial form params!");
    },
    testMappingParams: (i) => i.operator1 === g.NONE,
    mapToFilterColumn: () => null,
    testMappingFilterColumn: (i) => !i.customFilters && !i.filters ? {} : !1
  }, t.EMPTY = {
    label: "sheets-filter.conditions.empty",
    operator: g.EMPTY,
    order: y.SECOND,
    numOfParameters: 0,
    getDefaultFormParams: () => {
      throw new Error("[FilterConditionItems.EMPTY]: should not have initial form params!");
    },
    testMappingParams: ({ operator1: i }) => i === g.EMPTY,
    mapToFilterColumn: () => ({ customFilters: { customFilters: [{ val: "" }] } }),
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.val === "" && o.operator === void 0 ? { operator1: g.EMPTY } : !1;
    }
  }, t.NOT_EMPTY = {
    label: "sheets-filter.conditions.not-empty",
    operator: g.NOT_EMPTY,
    order: y.SECOND,
    numOfParameters: 0,
    getDefaultFormParams: () => {
      throw new Error("[FilterConditionItems.NOT_EMPTY]: should not have initial form params!");
    },
    testMappingParams: ({ operator1: i }) => i === g.NOT_EMPTY,
    mapToFilterColumn: () => ({ customFilters: { customFilters: [{ val: "", operator: u.NOT_EQUALS }] } }),
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.val === " " && o.operator === u.NOT_EQUALS ? { operator1: g.NOT_EMPTY } : !1;
    }
  }, t.TEXT_CONTAINS = {
    label: "sheets-filter.conditions.text-contains",
    operator: g.CONTAINS,
    order: y.FIRST,
    numOfParameters: 1,
    getDefaultFormParams: () => ({ operator1: g.CONTAINS, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === g.CONTAINS;
    },
    mapToFilterColumn: (i) => {
      const { val1: o } = i;
      return o === "" ? null : {
        customFilters: { customFilters: [{ val: `*${o}*` }] }
      };
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0], l = o.val.toString();
      return !o.operator && l.startsWith("*") && l.endsWith("*") ? { operator1: g.CONTAINS, val1: l.slice(1, -1) } : !1;
    }
  }, t.DOES_NOT_CONTAIN = {
    label: "sheets-filter.conditions.does-not-contain",
    operator: g.DOES_NOT_CONTAIN,
    order: y.FIRST,
    numOfParameters: 1,
    getDefaultFormParams: () => ({ operator1: g.DOES_NOT_CONTAIN, val1: "" }),
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: `*${i.val1}*`, operator: u.NOT_EQUALS }] }
    }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === g.DOES_NOT_CONTAIN;
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0], l = o.val.toString();
      return o.operator === u.NOT_EQUALS && l.startsWith("*") && l.endsWith("*") ? { operator1: g.DOES_NOT_CONTAIN, val1: l.slice(1, -1) } : !1;
    }
  }, t.STARTS_WITH = {
    label: "sheets-filter.conditions.starts-with",
    operator: g.STARTS_WITH,
    order: y.FIRST,
    numOfParameters: 1,
    getDefaultFormParams: () => ({ operator1: g.STARTS_WITH, val1: "" }),
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: `${i.val1}*` }] }
    }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === g.STARTS_WITH;
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0], l = o.val.toString();
      return !o.operator && l.endsWith("*") && !l.startsWith("*") ? { operator1: g.STARTS_WITH, val1: l.slice(0, -1) } : !1;
    }
  }, t.ENDS_WITH = {
    label: "sheets-filter.conditions.ends-with",
    operator: g.ENDS_WITH,
    order: y.FIRST,
    numOfParameters: 1,
    getDefaultFormParams: () => ({ operator1: g.ENDS_WITH, val1: "" }),
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: `*${i.val1}` }] }
    }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === g.ENDS_WITH;
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0], l = o.val.toString();
      return !o.operator && l.startsWith("*") && !l.endsWith("*") ? { operator1: g.ENDS_WITH, val1: l.slice(1) } : !1;
    }
  }, t.EQUALS = {
    label: "sheets-filter.conditions.equals",
    operator: g.EQUALS,
    order: y.FIRST,
    numOfParameters: 1,
    getDefaultFormParams: () => ({ operator1: g.EQUALS, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === g.EQUALS;
    },
    mapToFilterColumn: (i) => {
      const { val1: o } = i;
      return o === "" ? null : {
        customFilters: { customFilters: [{ val: o }] }
      };
    },
    testMappingFilterColumn: (i) => {
      var o, l, c;
      return ((l = (o = i.filters) == null ? void 0 : o.filters) == null ? void 0 : l.length) === 1 ? { operator1: g.EQUALS, val1: "" } : ((c = i.customFilters) == null ? void 0 : c.customFilters.length) === 1 && !i.customFilters.customFilters[0].operator ? { operator1: g.EQUALS, val1: i.customFilters.customFilters[0].val.toString() } : !1;
    }
  }, t.GREATER_THAN = {
    label: "sheets-filter.conditions.greater-than",
    operator: u.GREATER_THAN,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.GREATER_THAN, val1: "" }),
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.GREATER_THAN }] }
    }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.GREATER_THAN;
    },
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.GREATER_THAN ? !1 : { operator1: u.GREATER_THAN, val1: o.val.toString() };
    }
  }, t.GREATER_THAN_OR_EQUAL = {
    label: "sheets-filter.conditions.greater-than-or-equal",
    operator: u.GREATER_THAN_OR_EQUAL,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.GREATER_THAN_OR_EQUAL, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.GREATER_THAN_OR_EQUAL;
    },
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.GREATER_THAN_OR_EQUAL }] }
    }),
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.GREATER_THAN_OR_EQUAL ? !1 : { operator1: u.GREATER_THAN_OR_EQUAL, val1: o.val.toString() };
    }
  }, t.LESS_THAN = {
    label: "sheets-filter.conditions.less-than",
    operator: u.LESS_THAN,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.LESS_THAN, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.LESS_THAN;
    },
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.LESS_THAN }] }
    }),
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.LESS_THAN ? !1 : { operator1: u.LESS_THAN, val1: o.val.toString() };
    }
  }, t.LESS_THAN_OR_EQUAL = {
    label: "sheets-filter.conditions.less-than-or-equal",
    operator: u.LESS_THAN_OR_EQUAL,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.LESS_THAN_OR_EQUAL, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.LESS_THAN_OR_EQUAL;
    },
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.LESS_THAN_OR_EQUAL }] }
    }),
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.LESS_THAN_OR_EQUAL ? !1 : { operator1: u.LESS_THAN_OR_EQUAL, val1: o.val.toString() };
    }
  }, t.EQUAL = {
    label: "sheets-filter.conditions.equal",
    operator: u.EQUAL,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.EQUAL, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.EQUAL;
    },
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.EQUAL }] }
    }),
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.EQUAL ? !1 : { operator1: u.EQUAL, val1: o.val.toString() };
    }
  }, t.NOT_EQUAL = {
    label: "sheets-filter.conditions.not-equal",
    operator: u.NOT_EQUALS,
    numOfParameters: 1,
    order: y.FIRST,
    getDefaultFormParams: () => ({ operator1: u.NOT_EQUALS, val1: "" }),
    testMappingParams: (i) => {
      const [o] = x(i);
      return o === u.NOT_EQUALS;
    },
    mapToFilterColumn: (i) => ({
      customFilters: { customFilters: [{ val: i.val1, operator: u.NOT_EQUALS }] }
    }),
    testMappingFilterColumn: (i) => {
      var l;
      if (((l = i.customFilters) == null ? void 0 : l.customFilters.length) !== 1)
        return !1;
      const o = i.customFilters.customFilters[0];
      return o.operator !== u.NOT_EQUALS ? !1 : { operator1: u.NOT_EQUALS, val1: o.val.toString() };
    }
  }, t.BETWEEN = {
    label: "sheets-filter.conditions.between",
    operator: g.BETWEEN,
    order: y.SECOND,
    numOfParameters: 2,
    getDefaultFormParams: () => ({
      and: !0,
      operator1: u.GREATER_THAN_OR_EQUAL,
      val1: "",
      operator2: u.LESS_THAN_OR_EQUAL,
      val2: ""
    }),
    testMappingParams: (i) => {
      const { and: o, operator1: l, operator2: c } = i;
      if (!o) return !1;
      const h = [l, c];
      return h.includes(u.GREATER_THAN_OR_EQUAL) && h.includes(u.LESS_THAN_OR_EQUAL);
    },
    mapToFilterColumn: (i) => {
      const { val1: o, val2: l, operator1: c } = i, h = c === u.GREATER_THAN_OR_EQUAL;
      return {
        customFilters: {
          and: ht.TRUE,
          customFilters: [
            { val: h ? o : l, operator: u.GREATER_THAN_OR_EQUAL },
            { val: h ? l : o, operator: u.LESS_THAN_OR_EQUAL }
          ]
        }
      };
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 2)
        return !1;
      const [o, l] = i.customFilters.customFilters;
      return o.operator === u.GREATER_THAN_OR_EQUAL && l.operator === u.LESS_THAN_OR_EQUAL && i.customFilters.and ? {
        and: !0,
        operator1: u.GREATER_THAN_OR_EQUAL,
        val1: o.val.toString(),
        operator2: u.LESS_THAN_OR_EQUAL,
        val2: l.val.toString()
      } : l.operator === u.GREATER_THAN_OR_EQUAL && o.operator === u.LESS_THAN_OR_EQUAL && i.customFilters.and ? {
        and: !0,
        operator1: u.GREATER_THAN_OR_EQUAL,
        val1: l.val.toString(),
        operator2: u.LESS_THAN_OR_EQUAL,
        val2: o.val.toLocaleString()
      } : !1;
    }
  }, t.NOT_BETWEEN = {
    label: "sheets-filter.conditions.not-between",
    operator: g.NOT_BETWEEN,
    order: y.SECOND,
    numOfParameters: 2,
    getDefaultFormParams: () => ({
      operator1: u.LESS_THAN,
      val1: "",
      operator2: u.GREATER_THAN,
      val2: ""
    }),
    testMappingParams: (i) => {
      const { and: o, operator1: l, operator2: c } = i;
      if (o) return !1;
      const h = [l, c];
      return h.includes(u.GREATER_THAN) && h.includes(u.LESS_THAN);
    },
    mapToFilterColumn: (i) => {
      const { val1: o, val2: l, operator1: c } = i, h = c === u.GREATER_THAN;
      return {
        customFilters: {
          customFilters: [
            { val: h ? o : l, operator: u.GREATER_THAN },
            { val: h ? l : o, operator: u.LESS_THAN }
          ]
        }
      };
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 2)
        return !1;
      const [o, l] = i.customFilters.customFilters;
      return o.operator === u.LESS_THAN && l.operator === u.GREATER_THAN && !i.customFilters.and ? {
        operator1: u.LESS_THAN,
        val1: o.val.toString(),
        operator2: u.GREATER_THAN,
        val2: l.val.toString()
      } : l.operator === u.LESS_THAN && o.operator === u.GREATER_THAN && !i.customFilters.and ? {
        operator1: u.GREATER_THAN,
        val1: l.val.toString(),
        operator2: u.LESS_THAN,
        val2: o.val.toLocaleString()
      } : !1;
    }
  }, t.CUSTOM = {
    label: "sheets-filter.conditions.custom",
    operator: g.CUSTOM,
    order: y.SECOND,
    numOfParameters: 2,
    getDefaultFormParams: () => ({
      operator1: g.NONE,
      val1: "",
      operator2: g.NONE,
      val2: ""
    }),
    testMappingParams: () => !0,
    mapToFilterColumn: (i) => {
      const { and: o, val1: l, val2: c, operator1: h, operator2: d } = i;
      function C(b, F) {
        for (const R of t.ALL_CONDITIONS)
          if (R.operator === b)
            return R.mapToFilterColumn({ val1: F, operator1: b });
      }
      const v = !h || h === t.NONE.operator, _ = !d || d === t.NONE.operator;
      if (v && _)
        return t.NONE.mapToFilterColumn({});
      if (v)
        return C(d, c);
      if (_)
        return C(h, l);
      const S = C(h, l), E = C(d, c), N = {
        customFilters: [
          S.customFilters.customFilters[0],
          E.customFilters.customFilters[0]
        ]
      };
      return o && (N.and = ht.TRUE), { customFilters: N };
    },
    testMappingFilterColumn: (i) => {
      var c;
      if (((c = i.customFilters) == null ? void 0 : c.customFilters.length) !== 2)
        return !1;
      const o = i.customFilters.customFilters.map((h) => a({ customFilters: { customFilters: [h] } })), l = {
        operator1: o[0][0].operator,
        val1: o[0][1].val1,
        operator2: o[1][0].operator,
        val2: o[1][1].val1
      };
      return i.customFilters.and && (l.and = !0), l;
    }
  }, t.ALL_CONDITIONS = [
    // ------------------------------
    t.NONE,
    // ------------------------------
    t.EMPTY,
    t.NOT_EMPTY,
    // ------------------------------
    t.TEXT_CONTAINS,
    t.DOES_NOT_CONTAIN,
    t.STARTS_WITH,
    t.ENDS_WITH,
    t.EQUALS,
    // ------------------------------
    t.GREATER_THAN,
    t.GREATER_THAN_OR_EQUAL,
    t.LESS_THAN,
    t.LESS_THAN_OR_EQUAL,
    t.EQUAL,
    t.NOT_EQUAL,
    t.BETWEEN,
    t.NOT_BETWEEN,
    // ------------------------------
    t.CUSTOM
  ];
  function e(i) {
    const o = t.ALL_CONDITIONS.find((l) => l.operator === i);
    if (!o)
      throw new Error(`[SheetsFilter]: no condition item found for operator: ${i}`);
    return o;
  }
  t.getItemByOperator = e;
  function r(i, o) {
    for (const l of t.ALL_CONDITIONS.filter((c) => c.numOfParameters === o))
      if (l.numOfParameters !== 0 && l.testMappingParams(i))
        return l;
    for (const l of t.ALL_CONDITIONS)
      if (l.testMappingParams(i))
        return l;
    throw new Error("[SheetsFilter]: no condition item can be mapped from the filter map params!");
  }
  t.testMappingParams = r;
  function s(i) {
    const o = t.ALL_CONDITIONS.find((l) => l.operator === i);
    return (o == null ? void 0 : o.numOfParameters) === 0 ? { operator1: o.operator } : o.getDefaultFormParams();
  }
  t.getInitialFormParams = s;
  function n(i, o) {
    return i.mapToFilterColumn(o);
  }
  t.mapToFilterColumn = n;
  function a(i) {
    if (!i)
      return [t.NONE, {}];
    for (const o of t.ALL_CONDITIONS) {
      const l = o.testMappingFilterColumn(i);
      if (l)
        return [o, l];
    }
    return [t.NONE, {}];
  }
  t.testMappingFilterColumn = a;
})(m || (m = {}));
function x(t) {
  const { operator1: e, operator2: r, val1: s, val2: n } = t;
  if (e && r)
    throw new Error("Both operator1 and operator2 are set!");
  if (!e && !r)
    throw new Error("Neither operator1 and operator2 and both not set!");
  return e ? [e, s] : [r, n];
}
function Xe(t) {
  const e = [], r = [];
  let s = 0, n = 0;
  function a(i) {
    i.leaf && (i.checked ? (e.push(i), s += i.count) : (r.push(i), n += i.count)), i.children && i.children.forEach(a);
  }
  return t.forEach(a), {
    checkedItems: e,
    uncheckedItems: r,
    checked: s,
    unchecked: n
  };
}
var hi = Object.getOwnPropertyDescriptor, di = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? hi(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, Ge = (t, e) => (r, s) => e(r, s, t);
const ct = "sheets-filter.generate-filter-values.service", Ae = At(ct), mi = ["yyyy-mm-dd", "yyyy-mm-dd;@", "yyyy/mm/dd;@", "yyyy/mm/dd hh:mm", "yyyy-m-d am/pm h:mm", "yyyy-MM-dd", "yyyy/MM/dd", "yyyy/mm/dd", 'yyyy"年"MM"月"dd"日"', "MM-dd", 'M"月"d"日"', "MM-dd A/P hh:mm"];
let qe = class extends de {
  constructor(t, e, r) {
    super(), this._localeService = t, this._univerInstanceService = e, this._logService = r;
  }
  async getFilterValues(t) {
    var d;
    const { unitId: e, subUnitId: r, filteredOutRowsByOtherColumns: s, filterColumn: n, filters: a, blankChecked: i, iterateRange: o, alreadyChecked: l } = t, c = this._univerInstanceService.getUnit(e), h = (d = this._univerInstanceService.getUnit(e)) == null ? void 0 : d.getSheetBySheetId(r);
    return !c || !h ? [] : (this._logService.debug("[SheetsGenerateFilterValuesService]", "getFilterValues for", { unitId: e, subUnitId: r }), Vt(
      a,
      this._localeService,
      o,
      h,
      new Set(s),
      n,
      new Set(l.map(String)),
      i,
      c.getStyles()
    ));
  }
};
qe = di([
  Ge(0, T(K)),
  Ge(1, He),
  Ge(2, lr)
], qe);
function Vt(t, e, r, s, n, a, i, o, l) {
  var N, b, F, R, w, q, se, ne, D, L;
  const c = /* @__PURE__ */ new Map(), h = /* @__PURE__ */ new Map(), d = "yyyy-mm-dd", C = new Set(mi), v = "empty", _ = !t && ((a == null ? void 0 : a.filterBy) === A.COLORS || (a == null ? void 0 : a.filterBy) === A.CONDITIONS) && ((N = a.filteredOutRows) == null ? void 0 : N.size);
  let S = 0;
  for (const $ of s.iterateByColumn(r, !1, !1)) {
    const { row: rr, rowSpan: ut = 1 } = $;
    let oe = 0;
    for (; oe < ut; ) {
      const ir = rr + oe;
      if (n.has(ir)) {
        oe++;
        continue;
      }
      const z = $ != null && $.value ? ar($.value) : "";
      if (!z) {
        S += 1, oe += ut;
        continue;
      }
      const Oe = (b = $.value) != null && b.v && !$.value.p ? (w = (R = l.get((F = $.value) == null ? void 0 : F.s)) == null ? void 0 : R.n) == null ? void 0 : w.pattern : "", sr = Oe && dt.getFormatInfo(Oe).isDate;
      if (Oe && sr && C.has(Oe)) {
        const G = (q = s.getCellRaw($.row, $.col)) == null ? void 0 : q.v;
        if (!G) {
          oe++;
          continue;
        }
        const le = dt.format(d, G), [O, k, fe] = le.split("-").map(Number);
        let J = c.get(`${O}`);
        J || (J = {
          title: `${O}`,
          key: `${O}`,
          children: [],
          count: 0,
          leaf: !1,
          checked: !1
        }, c.set(`${O}`, J), h.set(`${O}`, [`${O}`]));
        let B = (se = J.children) == null ? void 0 : se.find((Ve) => Ve.key === `${O}-${k}`);
        B || (B = {
          title: e.t(`sheets-filter.date.${k}`),
          key: `${O}-${k}`,
          children: [],
          count: 0,
          leaf: !1,
          checked: !1
        }, (ne = J.children) == null || ne.push(B), h.set(`${O}-${k}`, [`${O}`, `${O}-${k}`]));
        const Qe = (D = B == null ? void 0 : B.children) == null ? void 0 : D.find((Ve) => Ve.key === `${O}-${k}-${fe}`);
        Qe ? (Qe.originValues.add(z), Qe.count++, B.count++, J.count++) : ((L = B.children) == null || L.push({
          title: `${fe}`,
          key: `${O}-${k}-${fe}`,
          count: 1,
          originValues: /* @__PURE__ */ new Set([z]),
          leaf: !0,
          checked: _ ? !1 : i.size ? i.has(z) : !o
        }), B.count++, J.count++, h.set(`${O}-${k}-${fe}`, [`${O}`, `${O}-${k}`, `${O}-${k}-${fe}`]));
      } else {
        const G = z;
        let le = c.get(G);
        le ? le.count++ : (le = {
          title: z,
          leaf: !0,
          checked: _ ? !1 : i.size ? i.has(z) : !o,
          key: G,
          count: 1
        }, c.set(G, le), h.set(G, [G]));
      }
      oe++;
    }
  }
  const E = _ ? !1 : t ? o : !0;
  if (S > 0) {
    const $ = {
      title: e.t("sheets-filter.panel.empty"),
      count: S,
      leaf: !0,
      checked: E,
      key: v
    };
    c.set("empty", $), h.set("empty", [v]);
  }
  return {
    filterTreeItems: fi(Array.from(c.values())),
    filterTreeMapCache: h
  };
}
function fi(t) {
  return Array.from(t).sort((e, r) => e.children && !r.children ? -1 : !e.children && r.children ? 1 : pi(e.title, r.title)).map((e) => (e.children && e.children.sort((r, s) => {
    const n = Number.parseInt(r.key.split("-")[1], 10), a = Number.parseInt(s.key.split("-")[1], 10);
    return n - a;
  }).forEach((r) => {
    r.children && r.children.sort((s, n) => {
      const a = Number.parseInt(s.key.split("-")[2], 10), i = Number.parseInt(n.key.split("-")[2], 10);
      return a - i;
    });
  }), e));
}
const Tt = (t) => !Number.isNaN(Number(t)) && !Number.isNaN(Number.parseFloat(t));
function pi(t, e) {
  const r = Tt(t), s = Tt(e);
  return r && s ? Number.parseFloat(t) - Number.parseFloat(e) : r && !s ? -1 : !r && s ? 1 : t.localeCompare(e);
}
function ze(t, e) {
  for (const r of t) {
    if (r.key === e)
      return r;
    if (r.children) {
      const s = ze(r.children, e);
      if (s)
        return s;
    }
  }
  return null;
}
function jt(t) {
  return t.leaf ? t.checked : t.children ? t.children.every((e) => jt(e)) : !0;
}
function ge(t, e) {
  t.leaf && (e !== void 0 ? t.checked = e : t.checked = !t.checked), t.children && t.children.forEach((r) => ge(r, e));
}
function Gt(t, e) {
  const r = [];
  return t.forEach((s) => {
    const n = s.originValues ? e.some(
      (o) => Array.from(s.originValues).some(
        (l) => l.toLowerCase().includes(o.toLowerCase())
      )
    ) : !1, a = !n && e.some(
      (o) => s.title.toLowerCase().includes(o.toLowerCase())
    );
    if (n || a)
      r.push({ ...s });
    else if (s.children) {
      const o = Gt(s.children, e);
      if (o.length > 0) {
        const l = o.reduce((c, h) => c + h.count, 0);
        r.push({ ...s, count: l, children: o });
      }
    }
  }), r;
}
var vi = Object.getOwnPropertyDescriptor, We = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? vi(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, Ce = (t, e) => (r, s) => e(r, s, t);
At("sheets-filter-ui.sheets-filter-panel.service");
let Z = class extends de {
  constructor(e, r) {
    super();
    p(this, "_filterBy$", new V(A.VALUES));
    p(this, "filterBy$", this._filterBy$.asObservable());
    p(this, "_filterByModel$", new Kr(1));
    p(this, "filterByModel$", this._filterByModel$.asObservable());
    p(this, "_filterByModel", null);
    p(this, "_hasCriteria$", new V(!1));
    p(this, "hasCriteria$", this._hasCriteria$.asObservable());
    p(this, "_filterModel", null);
    p(this, "_col$", new V(-1));
    p(this, "col$", this._col$.asObservable());
    p(this, "_filterHeaderListener", null);
    this._injector = e, this._refRangeService = r;
  }
  get filterBy() {
    return this._filterBy$.getValue();
  }
  get filterByModel() {
    return this._filterByModel;
  }
  set filterByModel(e) {
    this._filterByModel = e, this._filterByModel$.next(e);
  }
  get filterModel() {
    return this._filterModel;
  }
  get col() {
    return this._col$.getValue();
  }
  dispose() {
    this._filterBy$.complete(), this._filterByModel$.complete(), this._hasCriteria$.complete();
  }
  setupCol(e, r) {
    this.terminate(), this._filterModel = e, this._col$.next(r);
    const s = e.getFilterColumn(r);
    if (s) {
      const n = s.getColumnData();
      if (n.customFilters) {
        this._hasCriteria$.next(!0), this._setupByConditions(e, r);
        return;
      }
      if (n.colorFilters) {
        this._hasCriteria$.next(!0), this._setupByColors(e, r);
        return;
      }
      if (n.filters) {
        this._hasCriteria$.next(!0), this._setupByValues(e, r);
        return;
      }
      this._hasCriteria$.next(!1), this._setupByValues(e, r);
      return;
    }
    this._hasCriteria$.next(!1), this._setupByValues(e, r);
  }
  changeFilterBy(e) {
    if (!this._filterModel || this.col === -1)
      return !1;
    switch (e) {
      case A.VALUES:
        this._setupByValues(this._filterModel, this.col);
        break;
      case A.COLORS:
        this._setupByColors(this._filterModel, this.col);
        break;
      case A.CONDITIONS:
        this._setupByConditions(this._filterModel, this.col);
        break;
    }
    return !0;
  }
  terminate() {
    return this._filterModel = null, this._col$.next(-1), this._disposeFilterHeaderChangeListener(), !0;
  }
  _disposeFilterHeaderChangeListener() {
    var e;
    (e = this._filterHeaderListener) == null || e.dispose(), this._filterHeaderListener = null;
  }
  _listenToFilterHeaderChange(e, r) {
    this._disposeFilterHeaderChangeListener();
    const s = e.unitId, n = e.subUnitId, a = e.getRange(), i = {
      startColumn: r,
      startRow: a.startRow,
      endRow: a.startRow,
      endColumn: r
    };
    this._filterHeaderListener = this._refRangeService.watchRange(s, n, i, (o, l) => {
      if (!l)
        this.terminate();
      else {
        const c = l.startColumn - o.startColumn;
        c !== 0 && this._filterByModel.deltaCol(c);
      }
    });
  }
  async _setupByValues(e, r) {
    this._disposePreviousModel();
    const s = e.getRange();
    if (s.startRow === s.endRow) return !1;
    const n = await we.fromFilterColumn(
      this._injector,
      e,
      r
    );
    return this.filterByModel = n, this._filterBy$.next(A.VALUES), this._listenToFilterHeaderChange(e, r), !0;
  }
  async _setupByColors(e, r) {
    this._disposePreviousModel();
    const s = e.getRange();
    if (s.startRow === s.endRow) return !1;
    const n = await Le.fromFilterColumn(
      this._injector,
      e,
      r
    );
    return this.filterByModel = n, this._filterBy$.next(A.COLORS), this._listenToFilterHeaderChange(e, r), !0;
  }
  _setupByConditions(e, r) {
    this._disposePreviousModel();
    const s = e.getRange();
    if (s.startRow === s.endRow) return !1;
    const n = Pe.fromFilterColumn(
      this._injector,
      e,
      r,
      e.getFilterColumn(r)
    );
    return this.filterByModel = n, this._filterBy$.next(A.CONDITIONS), this._listenToFilterHeaderChange(e, r), !0;
  }
  _disposePreviousModel() {
    var e;
    (e = this._filterByModel) == null || e.dispose(), this.filterByModel = null;
  }
};
Z = We([
  Ce(0, T(re)),
  Ce(1, T(Br))
], Z);
let Pe = class extends de {
  constructor(e, r, s, n, a) {
    super();
    p(this, "canApply$", me(!0));
    p(this, "_conditionItem$");
    p(this, "conditionItem$");
    p(this, "_filterConditionFormParams$");
    p(this, "filterConditionFormParams$");
    this._filterModel = e, this.col = r, this._commandService = a, this._conditionItem$ = new V(s), this.conditionItem$ = this._conditionItem$.asObservable(), this._filterConditionFormParams$ = new V(n), this.filterConditionFormParams$ = this._filterConditionFormParams$.asObservable();
  }
  /**
   * Create a model with targeting filter column. If there is not a filter column, the model would be created with
   * default values.
   *
   * @param injector
   * @param filterModel
   * @param col
   * @param filterColumn
   *
   * @returns the model to control the panel's state
   */
  static fromFilterColumn(e, r, s, n) {
    const [a, i] = m.testMappingFilterColumn(n == null ? void 0 : n.getColumnData());
    return e.createInstance(Pe, r, s, a, i);
  }
  get conditionItem() {
    return this._conditionItem$.getValue();
  }
  get filterConditionFormParams() {
    return this._filterConditionFormParams$.getValue();
  }
  dispose() {
    super.dispose(), this._conditionItem$.complete(), this._filterConditionFormParams$.complete();
  }
  deltaCol(e) {
    this.col += e;
  }
  clear() {
    return this._disposed ? Promise.resolve(!1) : this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: null
    });
  }
  /**
   * Apply the filter condition to the target filter column.
   */
  async apply() {
    if (this._disposed) return !1;
    const e = m.mapToFilterColumn(this.conditionItem, this.filterConditionFormParams);
    return this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: e
    });
  }
  /**
   * This method would be called when user changes the primary condition. The model would load the corresponding
   * `IFilterConditionFormParams` and load default condition form params.
   */
  onPrimaryConditionChange(e) {
    const r = m.ALL_CONDITIONS.find((s) => s.operator === e);
    if (!r)
      throw new Error(`[ByConditionsModel]: condition item not found for operator: ${e}!`);
    this._conditionItem$.next(r), this._filterConditionFormParams$.next(m.getInitialFormParams(e));
  }
  /**
   * This method would be called when user changes the primary conditions, the input values or "AND" "OR" ratio.
   * If the primary conditions or the ratio is changed, the method would load the corresponding `IFilterCondition`.
   *
   * When the panel call this method, it only has to pass the changed keys.
   *
   * @param params
   */
  onConditionFormChange(e) {
    const r = { ...this.filterConditionFormParams, ...e };
    if (r.and !== !0 && delete r.and, typeof e.and < "u" || typeof e.operator1 < "u" || typeof e.operator2 < "u") {
      const s = m.testMappingParams(r, this.conditionItem.numOfParameters);
      this._conditionItem$.next(s);
    }
    this._filterConditionFormParams$.next(r);
  }
};
Pe = We([
  Ce(4, j)
], Pe);
let we = class extends de {
  constructor(e, r, s, n, a) {
    super();
    p(this, "_rawFilterItems$");
    p(this, "rawFilterItems$");
    p(this, "filterItems$");
    p(this, "_filterItems", []);
    p(this, "_treeMapCache");
    p(this, "canApply$");
    p(this, "_manuallyUpdateFilterItems$");
    p(this, "_searchString$");
    p(this, "searchString$");
    this._filterModel = e, this.col = r, this._commandService = a, this._treeMapCache = n, this._searchString$ = new V(""), this.searchString$ = this._searchString$.asObservable(), this._rawFilterItems$ = new V(s), this.rawFilterItems$ = this._rawFilterItems$.asObservable(), this._manuallyUpdateFilterItems$ = new Xr(), this.filterItems$ = qr(
      zr([
        this._searchString$.pipe(
          Dt(500, void 0, { leading: !0, trailing: !0 }),
          Bt(void 0)
        ),
        this._rawFilterItems$
      ]).pipe(
        ce(([i, o]) => {
          if (!i) return o;
          const c = i.toLowerCase().split(/\s+/).filter((h) => !!h);
          return Gt(o, c);
        })
      ),
      this._manuallyUpdateFilterItems$
    ).pipe(Jr(1)), this.canApply$ = this.filterItems$.pipe(ce((i) => Xe(i).checked > 0)), this.disposeWithMe(this.filterItems$.subscribe((i) => this._filterItems = i));
  }
  /**
   * Create a model with targeting filter column. If there is not a filter column, the model would be created with
   * default values.
   *
   * @param injector
   * @param filterModel
   * @param col
   *
   * @returns the model to control the panel's state
   */
  static async fromFilterColumn(e, r, s) {
    const n = e.get(He), a = e.get(K), i = e.get(Ae, Pt.OPTIONAL), { unitId: o, subUnitId: l } = r, c = n.getUniverSheetInstance(o);
    if (!c) throw new Error(`[ByValuesModel]: Workbook not found for filter model with unitId: ${o}!`);
    const h = c == null ? void 0 : c.getSheetBySheetId(l);
    if (!h) throw new Error(`[ByValuesModel]: Worksheet not found for filter model with unitId: ${o} and subUnitId: ${l}!`);
    const d = r.getRange(), C = s, v = r.getFilterColumn(s), _ = v == null ? void 0 : v.getColumnData().filters, S = new Set(_ == null ? void 0 : _.filters), E = !!(_ && _.blank), N = r.getFilteredOutRowsExceptCol(s), b = { ...d, startRow: d.startRow + 1, startColumn: C, endColumn: C };
    let F, R;
    if (i) {
      const w = await i.getFilterValues({
        unitId: o,
        subUnitId: l,
        filteredOutRowsByOtherColumns: Array.from(N),
        filterColumn: v,
        filters: !!_,
        blankChecked: E,
        iterateRange: b,
        alreadyChecked: Array.from(S)
      });
      F = w.filterTreeItems, R = w.filterTreeMapCache;
    } else {
      const w = Vt(
        !!_,
        a,
        b,
        h,
        N,
        v,
        S,
        E,
        c.getStyles()
      );
      F = w.filterTreeItems, R = w.filterTreeMapCache;
    }
    return e.createInstance(we, r, s, F, R);
  }
  get rawFilterItems() {
    return this._rawFilterItems$.getValue();
  }
  get filterItems() {
    return this._filterItems;
  }
  get treeMapCache() {
    return this._treeMapCache;
  }
  dispose() {
    this._rawFilterItems$.complete(), this._searchString$.complete();
  }
  deltaCol(e) {
    this.col += e;
  }
  setSearchString(e) {
    this._searchString$.next(e);
  }
  onCheckAllToggled(e) {
    const r = be.deepClone(this._filterItems);
    r.forEach((s) => ge(s, e)), this._manuallyUpdateFilterItems(r);
  }
  /**
   * Toggle a filter item.
   */
  onFilterCheckToggled(e) {
    const r = be.deepClone(this._filterItems), s = ze(r, e.key);
    if (!s)
      return;
    const n = jt(s);
    ge(s, !n), this._manuallyUpdateFilterItems(r);
  }
  onFilterOnly(e) {
    const r = be.deepClone(this._filterItems);
    r.forEach((s) => ge(s, !1)), e.forEach((s) => {
      const n = ze(r, s);
      n && ge(n, !0);
    }), this._manuallyUpdateFilterItems(r);
  }
  _manuallyUpdateFilterItems(e) {
    this._manuallyUpdateFilterItems$.next(e);
  }
  // expose method here to let the panel change filter items
  // #region ByValuesModel apply methods
  clear() {
    return this._disposed ? Promise.resolve(!1) : this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: null
    });
  }
  /**
   * Apply the filter condition to the target filter column.
   */
  async apply() {
    if (this._disposed)
      return !1;
    const e = Xe(this._filterItems), { checked: r, checkedItems: s } = e, n = this.rawFilterItems;
    let a = 0;
    for (const c of n)
      a += c.count;
    const i = r === 0, o = e.checked === a, l = { colId: this.col };
    if (i)
      throw new Error("[ByValuesModel]: no checked items!");
    if (o)
      return this._commandService.executeCommand(Q.id, {
        unitId: this._filterModel.unitId,
        subUnitId: this._filterModel.subUnitId,
        col: this.col,
        criteria: null
      });
    {
      l.filters = {};
      const c = s.filter((d) => d.key !== "empty");
      c.length > 0 && (l.filters = {
        filters: c.flatMap((d) => d.originValues ? Array.from(d.originValues) : [d.title])
      }), c.length !== s.length && (l.filters.blank = !0);
    }
    return this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: l
    });
  }
  // #endregion
};
we = We([
  Ce(4, j)
], we);
let Le = class extends de {
  constructor(e, r, s, n, a) {
    super();
    p(this, "canApply$", me(!0));
    p(this, "_cellFillColors$");
    p(this, "cellFillColors$");
    p(this, "_cellTextColors$");
    p(this, "cellTextColors$");
    this._filterModel = e, this.col = r, this._commandService = a, this._cellFillColors$ = new V(Array.from(s.values())), this.cellFillColors$ = this._cellFillColors$.asObservable(), this._cellTextColors$ = new V(Array.from(n.values())), this.cellTextColors$ = this._cellTextColors$.asObservable();
  }
  /**
   * Create a model with targeting filter column. If there is not a filter column, the model would be created with
   * default values.
   *
   * @param injector
   * @param filterModel
   * @param col
   *
   * @returns the model to control the panel's state
   */
  static async fromFilterColumn(e, r, s) {
    var b, F, R;
    const n = e.get(He), { unitId: a, subUnitId: i } = r, o = n.getUniverSheetInstance(a);
    if (!o) throw new Error(`[ByColorsModel]: Workbook not found for filter model with unitId: ${a}!`);
    const l = o == null ? void 0 : o.getSheetBySheetId(i);
    if (!l) throw new Error(`[ByColorsModel]: Worksheet not found for filter model with unitId: ${a} and subUnitId: ${i}!`);
    const c = r.getRange(), h = s, d = (b = r.getFilterColumn(s)) == null ? void 0 : b.getColumnData().colorFilters, C = r.getFilteredOutRowsExceptCol(s), v = { ...c, startRow: c.startRow + 1, startColumn: h, endColumn: h }, _ = /* @__PURE__ */ new Map(), S = new Set((F = d == null ? void 0 : d.cellFillColors) != null ? F : []), E = /* @__PURE__ */ new Map(), N = new Set((R = d == null ? void 0 : d.cellTextColors) != null ? R : []);
    for (const w of l.iterateByColumn(v, !1, !0)) {
      const { row: q, col: se, value: ne } = w;
      if (C.has(q))
        continue;
      const D = l.getComposedCellStyleByCellData(q, se, ne);
      if (D.bg && D.bg.rgb) {
        const L = new mt(D.bg.rgb).toRgbString();
        _.has(L) || _.set(L, { color: L, checked: S.has(L) });
      } else
        _.set("default-fill-color", { color: null, checked: S.has(null) });
      if (D.cl && D.cl.rgb) {
        const L = new mt(D.cl.rgb).toRgbString();
        E.has(L) || E.set(L, { color: L, checked: N.has(L) });
      } else
        E.set("default-font-color", { color: vt, checked: N.has(vt) });
    }
    return e.createInstance(Le, r, s, _, E);
  }
  get cellFillColors() {
    return this._cellFillColors$.getValue();
  }
  get cellTextColors() {
    return this._cellTextColors$.getValue();
  }
  dispose() {
    super.dispose(), this._cellFillColors$.complete();
  }
  deltaCol(e) {
    this.col += e;
  }
  // expose method here to let the panel change filter items
  // #region ByColorsModel apply methods
  clear() {
    return this._disposed ? Promise.resolve(!1) : this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: null
    });
  }
  onFilterCheckToggled(e, r = !0) {
    const s = r ? this.cellFillColors : this.cellTextColors, n = [];
    let a = !1;
    for (let i = 0; i < s.length; i++) {
      const o = s[i];
      if (o.color === e.color) {
        a = !0, n.push({
          color: o.color,
          checked: !o.checked
        });
        continue;
      }
      n.push({
        color: o.color,
        checked: o.checked
      });
    }
    a && (this._resetColorsCheckedStatus(!r), r ? this._cellFillColors$.next([...n]) : this._cellTextColors$.next([...n]));
  }
  _resetColorsCheckedStatus(e = !0) {
    const r = e ? this.cellFillColors : this.cellTextColors, s = [];
    for (let n = 0; n < r.length; n++)
      s.push({
        color: r[n].color,
        checked: !1
      });
    e ? this._cellFillColors$.next([...s]) : this._cellTextColors$.next([...s]);
  }
  /**
   * Apply the filter condition to the target filter column.
   */
  async apply() {
    if (this._disposed)
      return !1;
    const e = this.cellFillColors.filter((n) => n.checked).map((n) => n.color), r = this.cellTextColors.filter((n) => n.checked).map((n) => n.color);
    if (e.length === 0 && r.length === 0)
      return this._commandService.executeCommand(Q.id, {
        unitId: this._filterModel.unitId,
        subUnitId: this._filterModel.subUnitId,
        col: this.col,
        criteria: null
      });
    const s = { colId: this.col };
    return e.length > 0 ? s.colorFilters = {
      cellFillColors: e
    } : r.length > 0 && (s.colorFilters = {
      cellTextColors: r
    }), this._commandService.executeCommand(Q.id, {
      unitId: this._filterModel.unitId,
      subUnitId: this._filterModel.subUnitId,
      col: this.col,
      criteria: s
    });
  }
  // #endregion
};
Le = We([
  Ce(4, j)
], Le);
const ue = "FILTER_PANEL_OPENED", $e = {
  id: "sheet.operation.open-filter-panel",
  type: De.OPERATION,
  handler: (t, e) => {
    const r = t.get(Be), s = t.get(X), n = t.get(Z);
    t.get(j).syncExecuteCommand(Cr.id, { visible: !1 });
    const { unitId: i, subUnitId: o, col: l } = e, c = s.getFilterModel(i, o);
    return c ? (n.setupCol(c, l), r.getContextValue(ue) || r.setContextValue(ue, !0), !0) : !1;
  }
}, Se = {
  id: "sheet.operation.close-filter-panel",
  type: De.OPERATION,
  handler: (t) => {
    const e = t.get(Be), r = t.get(Z), s = t.get(Pr, Pt.OPTIONAL);
    return e.getContextValue(ue) ? (e.setContextValue(ue, !1), s == null || s.focus(), r.terminate()) : !1;
  }
}, Yt = {
  id: "sheet.operation.apply-filter",
  type: De.OPERATION,
  handler: (t, e) => {
    const { filterBy: r } = e;
    return t.get(Z).changeFilterBy(r);
  }
}, Zt = "sheets-filter-ui.config", Me = {};
var _i = Object.getOwnPropertyDescriptor, gi = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? _i(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, ae = (t, e) => (r, s) => e(r, s, t);
let he = class extends de {
  constructor(t, e, r, s, n, a) {
    super(), this._sheetsFilterService = t, this._localeService = e, this._commandService = r, this._sheetPermissionCheckPermission = s, this._injector = n, this._sheetsSelectionService = a, this._commandExecutedListener();
  }
  _commandExecutedListener() {
    this.disposeWithMe(
      this._commandService.beforeCommandExecuted((t) => {
        var e, r, s;
        if (t.id === Ee.id) {
          const n = this._injector.get(He), a = Vr(n);
          if (!a) return;
          const { unitId: i, subUnitId: o, worksheet: l } = a, c = (e = this._sheetsFilterService.getFilterModel(i, o)) == null ? void 0 : e.getRange();
          let h;
          if (c)
            h = this._sheetPermissionCheckPermission.permissionCheckWithRanges({
              rangeTypes: [_e],
              worksheetTypes: [pe, ve]
            }, [c]);
          else {
            const d = (r = this._sheetsSelectionService.getCurrentLastSelection()) == null ? void 0 : r.range;
            if (d) {
              let C = { ...d };
              C = d.startColumn === d.endColumn && d.startRow === d.endRow ? jr(C, { left: !0, right: !0, up: !0, down: !0 }, l) : C, h = this._sheetPermissionCheckPermission.permissionCheckWithRanges({
                rangeTypes: [_e],
                worksheetTypes: [ve, pe]
              }, [C], i, o);
            } else
              h = this._sheetPermissionCheckPermission.permissionCheckWithoutRange({
                rangeTypes: [_e],
                worksheetTypes: [ve, pe]
              });
          }
          h || this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr"));
        }
        if (t.id === $e.id) {
          const n = t.params, { unitId: a, subUnitId: i } = n, o = (s = this._sheetsFilterService.getFilterModel(a, i)) == null ? void 0 : s.getRange(), l = be.deepClone(o);
          l && (l.startColumn = n.col, l.endColumn = n.col, this._sheetPermissionCheckPermission.permissionCheckWithRanges({
            rangeTypes: [_e],
            worksheetTypes: [pe, ve]
          }, [l]) || this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr")));
        }
      })
    );
  }
};
he = gi([
  ae(0, T(X)),
  ae(1, T(K)),
  ae(2, j),
  ae(3, T(Wr)),
  ae(4, T(re)),
  ae(5, T(Qr))
], he);
const Y = 16, Et = new Path2D("M4 6L8 10L12 6Z");
class Nt {
  static drawNoCriteria(e, r, s, n) {
    e.save(), _t.drawWith(e, {
      radius: 2,
      width: Y,
      height: Y,
      fill: n
    }), e.scale(r / Y, r / Y), e.fillStyle = s, e.fill(Et), e.restore();
  }
  static drawHasCriteria(e, r, s, n) {
    e.save(), _t.drawWith(e, {
      radius: 2,
      width: Y,
      height: Y,
      fill: n
    }), e.scale(r / Y, r / Y), e.fillStyle = s, e.fill(Et), e.restore();
  }
}
var Si = Object.getOwnPropertyDescriptor, Ci = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? Si(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, Ye = (t, e) => (r, s) => e(r, s, t);
const W = 16, Ze = 1;
let Je = class extends Dr {
  constructor(e, r, s, n, a) {
    super(e, r);
    p(this, "_cellWidth", 0);
    p(this, "_cellHeight", 0);
    p(this, "_filterParams");
    p(this, "_hovered", !1);
    this._contextService = s, this._commandService = n, this._themeService = a, this.setShapeProps(r), this.onPointerDown$.subscribeEvent((i) => this.onPointerDown(i)), this.onPointerEnter$.subscribeEvent(() => this.onPointerEnter()), this.onPointerLeave$.subscribeEvent(() => this.onPointerLeave());
  }
  setShapeProps(e) {
    typeof e.cellHeight < "u" && (this._cellHeight = e.cellHeight), typeof e.cellWidth < "u" && (this._cellWidth = e.cellWidth), typeof e.filterParams < "u" && (this._filterParams = e.filterParams), this.transformByState({
      width: e.width,
      height: e.height
    });
  }
  _draw(e) {
    const r = this._cellHeight, s = this._cellWidth, n = W - s, a = W - r;
    e.save();
    const i = new Path2D();
    i.rect(n, a, s, r), e.clip(i);
    const { hasCriteria: o } = this._filterParams, l = o ? this._themeService.getColorFromTheme("primary.600") : this._themeService.getColorFromTheme("black"), c = this._hovered ? this._themeService.getColorFromTheme("gray.50") : "rgba(255, 255, 255, 1.0)";
    o ? Nt.drawHasCriteria(e, W, l, c) : Nt.drawNoCriteria(e, W, l, c), e.restore();
  }
  onPointerDown(e) {
    if (e.button === 2)
      return;
    const { col: r, unitId: s, subUnitId: n } = this._filterParams;
    this._contextService.getContextValue(ue) || !this._commandService.hasCommand($e.id) || setTimeout(() => {
      this._commandService.executeCommand($e.id, {
        unitId: s,
        subUnitId: n,
        col: r
      });
    }, 200);
  }
  onPointerEnter() {
    this._hovered = !0, this.makeDirty(!0);
  }
  onPointerLeave() {
    this._hovered = !1, this.makeDirty(!0);
  }
};
Je = Ci([
  Ye(2, Be),
  Ye(3, j),
  Ye(4, T(wt))
], Je);
var Ti = Object.getOwnPropertyDescriptor, Ei = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? Ti(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, ee = (t, e) => (r, s) => e(r, s, t);
const Ni = 1e3, Fi = 5e3;
let et = class extends Lt {
  constructor(e, r, s, n, a, i, o, l) {
    super();
    p(this, "_filterRangeShape", null);
    p(this, "_buttonRenderDisposable", null);
    p(this, "_filterButtonShapes", []);
    this._context = e, this._injector = r, this._sheetSkeletonManagerService = s, this._sheetsFilterService = n, this._themeService = a, this._sheetInterceptorService = i, this._commandService = o, this._selectionRenderService = l, this._initRenderer();
  }
  dispose() {
    super.dispose(), this._disposeRendering();
  }
  _initRenderer() {
    this._sheetSkeletonManagerService.currentSkeleton$.pipe(
      ot((e) => {
        var o, l;
        if (!e) return me(null);
        const { unit: r, unitId: s } = this._context, n = ((o = r.getActiveSheet()) == null ? void 0 : o.getSheetId()) || "", a = (l = this._sheetsFilterService.getFilterModel(s, n)) != null ? l : void 0, i = () => ({
          unitId: s,
          worksheetId: n,
          filterModel: a,
          range: a == null ? void 0 : a.getRange(),
          skeleton: e.skeleton
        });
        return cr(this._commandService.onCommandExecuted.bind(this._commandService)).pipe(
          ei(
            ([c]) => {
              var h;
              return c.type === De.MUTATION && ((h = c.params) == null ? void 0 : h.unitId) === r.getUnitId() && (mr.has(c.id) || c.id === Yr.id);
            }
          ),
          Dt(20, void 0, { leading: !1, trailing: !0 }),
          ce(i),
          Bt(i())
          // must trigger once
        );
      }),
      ti(this.dispose$)
    ).subscribe((e) => {
      this._disposeRendering(), !(!e || !e.range) && (this._renderRange(e.range, e.skeleton), this._renderButtons(e));
    });
  }
  _renderRange(e, r) {
    const { scene: s } = this._context, { rowHeaderWidth: n, columnHeaderHeight: a } = r, i = this._filterRangeShape = new Nr(
      s,
      Ni,
      this._themeService,
      {
        rowHeaderWidth: n,
        columnHeaderHeight: a,
        enableAutoFill: !1,
        highlightHeader: !1
      }
    ), l = Fr({
      range: e,
      primary: null,
      style: { fill: "rgba(0, 0, 0, 0.0)" }
    }, r);
    i.updateRangeBySelectionWithCoord(l), i.setEvent(!1), s.makeDirty(!0);
  }
  _renderButtons(e) {
    const { range: r, filterModel: s, unitId: n, skeleton: a, worksheetId: i } = e, { scene: o } = this._context;
    this._interceptCellContent(n, i, e.range);
    const { startColumn: l, endColumn: c, startRow: h } = r;
    for (let d = l; d <= c; d++) {
      const C = `sheets-filter-button-${d}`, v = yr(h, d, o, a), { startX: _, startY: S, endX: E, endY: N } = v, b = E - _, F = N - S;
      if (F <= Ze || b <= Ze)
        continue;
      const R = !!s.getFilterColumn(d), w = E - W - Ze, q = S + (F - W) / 2, se = {
        left: w,
        top: q,
        height: W,
        width: W,
        zIndex: Fi,
        cellHeight: F,
        cellWidth: b,
        filterParams: { unitId: n, subUnitId: i, col: d, hasCriteria: R }
      }, ne = this._injector.createInstance(Je, C, se);
      this._filterButtonShapes.push(ne);
    }
    o.addObjects(this._filterButtonShapes), o.makeDirty();
  }
  _interceptCellContent(e, r, s) {
    const { startRow: n, startColumn: a, endColumn: i } = s;
    this._buttonRenderDisposable = this._sheetInterceptorService.intercept(Zr.CELL_CONTENT, {
      effect: ur.Style,
      handler: (o, l, c) => {
        const { row: h, col: d, unitId: C, subUnitId: v } = l;
        return C !== e || v !== r || h !== n || d < a || d > i || ((!o || o === l.rawData) && (o = { ...l.rawData }), o.fontRenderExtension = {
          ...o == null ? void 0 : o.fontRenderExtension,
          rightOffset: W
        }), c(o);
      },
      priority: 10
    });
  }
  _disposeRendering() {
    var e, r;
    (e = this._filterRangeShape) == null || e.dispose(), this._filterButtonShapes.forEach((s) => s.dispose()), (r = this._buttonRenderDisposable) == null || r.dispose(), this._filterRangeShape = null, this._buttonRenderDisposable = null, this._filterButtonShapes = [];
  }
};
et = Ei([
  ee(1, T(re)),
  ee(2, T(Tr)),
  ee(3, T(X)),
  ee(4, T(wt)),
  ee(5, T(Gr)),
  ee(6, j),
  ee(7, Er)
], et);
var yi = Object.getOwnPropertyDescriptor, Oi = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? yi(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, Ft = (t, e) => (r, s) => e(r, s, t);
let Te = class extends Lt {
  constructor(t, e) {
    super(), this._renderManagerService = t, this._sheetsRenderService = e, [
      fr,
      pr,
      vr,
      _r
    ].forEach((r) => this.disposeWithMe(this._sheetsRenderService.registerSkeletonChangingMutations(r.id))), this.disposeWithMe(this._renderManagerService.registerRenderModule(
      ie.UNIVER_SHEET,
      [et]
    ));
  }
};
Te = Oi([
  Ft(0, Ht),
  Ft(1, T(kt))
], Te);
var Ii = Object.defineProperty, bi = Object.getOwnPropertyDescriptor, Ri = (t, e, r) => e in t ? Ii(t, e, { enumerable: !0, configurable: !0, writable: !0, value: r }) : t[e] = r, Ai = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? bi(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, yt = (t, e) => (r, s) => e(r, s, t), Kt = (t, e, r) => Ri(t, typeof e != "symbol" ? e + "" : e, r);
const Pi = "SHEET_FILTER_UI_PLUGIN";
let xe = class extends tt {
  constructor(t = Me, e, r) {
    super(), this._config = t, this._injector = e, this._configService = r;
    const { menu: s, ...n } = xt(
      {},
      Me,
      this._config
    );
    s && this._configService.setConfig("menu", s, { merge: !0 }), this._configService.setConfig(Zt, n);
  }
  onStarting() {
    [
      [he],
      [Te]
    ].forEach((t) => this._injector.add(t));
  }
  onReady() {
    this._injector.get(he);
  }
  onRendered() {
    this._injector.get(Te);
  }
};
Kt(xe, "type", ie.UNIVER_SHEET);
Kt(xe, "pluginName", Pi);
xe = Ai([
  $t(Ut),
  yt(1, T(re)),
  yt(2, Mt)
], xe);
var M = function() {
  return M = Object.assign || function(t) {
    for (var e, r = 1, s = arguments.length; r < s; r++) {
      e = arguments[r];
      for (var n in e) Object.prototype.hasOwnProperty.call(e, n) && (t[n] = e[n]);
    }
    return t;
  }, M.apply(this, arguments);
}, wi = function(t, e) {
  var r = {};
  for (var s in t) Object.prototype.hasOwnProperty.call(t, s) && e.indexOf(s) < 0 && (r[s] = t[s]);
  if (t != null && typeof Object.getOwnPropertySymbols == "function")
    for (var n = 0, s = Object.getOwnPropertySymbols(t); n < s.length; n++)
      e.indexOf(s[n]) < 0 && Object.prototype.propertyIsEnumerable.call(t, s[n]) && (r[s[n]] = t[s[n]]);
  return r;
}, ye = Ne(function(t, e) {
  var r = t.icon, s = t.id, n = t.className, a = t.extend, i = wi(t, ["icon", "id", "className", "extend"]), o = "univerjs-icon univerjs-icon-".concat(s, " ").concat(n || "").trim(), l = ui("_".concat(Mi()));
  return Xt(r, "".concat(s), { defIds: r.defIds, idSuffix: l.current }, M({ ref: e, className: o }, i), a);
});
function Xt(t, e, r, s, n) {
  return Fe(t.tag, M(M({ key: e }, Li(t, r, n)), s), ($i(t, r).children || []).map(function(a, i) {
    return Xt(a, "".concat(e, "-").concat(t.tag, "-").concat(i), r, void 0, n);
  }));
}
function Li(t, e, r) {
  var s = M({}, t.attrs);
  r != null && r.colorChannel1 && s.fill === "colorChannel1" && (s.fill = r.colorChannel1), t.tag === "mask" && s.id && (s.id = s.id + e.idSuffix), Object.entries(s).forEach(function(a) {
    var i = a[0], o = a[1];
    i === "mask" && typeof o == "string" && (s[i] = o.replace(/url\(#(.*)\)/, "url(#$1".concat(e.idSuffix, ")")));
  });
  var n = e.defIds;
  return !n || n.length === 0 || (t.tag === "use" && s["xlink:href"] && (s["xlink:href"] = s["xlink:href"] + e.idSuffix), Object.entries(s).forEach(function(a) {
    var i = a[0], o = a[1];
    typeof o == "string" && (s[i] = o.replace(/url\(#(.*)\)/, "url(#$1".concat(e.idSuffix, ")")));
  })), s;
}
function $i(t, e) {
  var r, s = e.defIds;
  return !s || s.length === 0 ? t : t.tag === "defs" && (!((r = t.children) === null || r === void 0) && r.length) ? M(M({}, t), { children: t.children.map(function(n) {
    return typeof n.attrs.id == "string" && s && s.includes(n.attrs.id) ? M(M({}, n), { attrs: M(M({}, n.attrs), { id: n.attrs.id + e.idSuffix }) }) : n;
  }) }) : t;
}
function Mi() {
  return Math.random().toString(36).substring(2, 8);
}
ye.displayName = "UniverIcon";
var xi = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 20 20", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M10 1.05957C10.356 1.05957 10.6816 1.26162 10.8408 1.58008L18.8408 17.5801L18.8799 17.668C19.0486 18.1134 18.8551 18.6232 18.4199 18.8408C17.9557 19.0727 17.3913 18.8841 17.1592 18.4199L10 4.10156L2.84082 18.4199C2.60871 18.8841 2.04434 19.0727 1.58008 18.8408C1.11587 18.6087 0.92731 18.0443 1.15918 17.5801L9.15918 1.58008C9.31841 1.26162 9.64395 1.05957 10 1.05957Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M15.3337 11.7261L15.4294 11.731C15.9035 11.779 16.2732 12.1798 16.2732 12.6665C16.2732 13.1532 15.9035 13.554 15.4294 13.602L15.3337 13.6069H4.66675C4.1476 13.6069 3.72632 13.1856 3.72632 12.6665C3.72632 12.1474 4.1476 11.7261 4.66675 11.7261H15.3337Z" } }] }, qt = Ne(function(t, e) {
  return Fe(ye, Object.assign({}, t, {
    id: "a-icon",
    ref: e,
    icon: xi
  }));
});
qt.displayName = "AIcon";
var Ui = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 20 20", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M17.0596 10C17.0596 6.10087 13.8992 2.94043 10 2.94043C6.10087 2.94043 2.94043 6.10087 2.94043 10C2.94043 13.8992 6.10087 17.0596 10 17.0596C13.8992 17.0596 17.0596 13.8992 17.0596 10ZM18.9404 10C18.9404 14.9374 14.9374 18.9404 10 18.9404C5.06257 18.9404 1.05957 14.9374 1.05957 10C1.05957 5.06257 5.06257 1.05957 10 1.05957C14.9374 1.05957 18.9404 5.06257 18.9404 10Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M4.29492 4.13476C4.63911 3.79057 5.1845 3.76906 5.55371 4.07031L5.625 4.13476L16.0244 14.5352L16.0889 14.6064C16.3902 14.9757 16.3686 15.52 16.0244 15.8643C15.6573 16.2313 15.0624 16.2313 14.6953 15.8643L4.29492 5.46484L4.23047 5.39355C3.92922 5.02434 3.95073 4.47895 4.29492 4.13476Z" } }] }, zt = Ne(function(t, e) {
  return Fe(ye, Object.assign({}, t, {
    id: "ban-icon",
    ref: e,
    icon: Ui
  }));
});
zt.displayName = "BanIcon";
var ki = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 17 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M3.32182 2.60967C2.98161 2.60967 2.79671 3.0074 3.01601 3.2675L6.85819 7.8246C6.94943 7.93282 6.99947 8.06981 6.99947 8.21136V12.7338C6.99947 12.898 7.0998 13.0455 7.2525 13.1058L8.73833 13.6928C9.00085 13.7965 9.28531 13.6031 9.28531 13.3208V8.21136C9.28531 8.06981 9.33535 7.93282 9.42659 7.8246L13.2688 3.2675C13.4881 3.0074 13.3032 2.60967 12.963 2.60967H3.32182ZM2.09858 4.04101C1.22139 3.0006 1.96097 1.40967 3.32182 1.40967H12.963C14.3238 1.40967 15.0634 3.0006 14.1862 4.04101L10.4853 8.43054V13.3208C10.4853 14.4498 9.34747 15.2237 8.29742 14.8089L6.81158 14.2219C6.20078 13.9806 5.79947 13.3905 5.79947 12.7338V8.43054L2.09858 4.04101Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, Jt = Ne(function(t, e) {
  return Fe(ye, Object.assign({}, t, {
    id: "filter-icon",
    ref: e,
    icon: ki
  }));
});
Jt.displayName = "FilterIcon";
var Hi = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM11.7245 6.42417C11.9588 6.18985 11.9588 5.80995 11.7245 5.57564C11.4901 5.34132 11.1102 5.34132 10.8759 5.57564L7.3002 9.15137L5.72446 7.57564C5.49014 7.34132 5.11025 7.34132 4.87593 7.57564C4.64162 7.80995 4.64162 8.18985 4.87593 8.42417L6.87593 10.4242C7.11025 10.6585 7.49014 10.6585 7.72446 10.4242L11.7245 6.42417Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, er = Ne(function(t, e) {
  return Fe(ye, Object.assign({}, t, {
    id: "success-icon",
    ref: e,
    icon: Hi
  }));
});
er.displayName = "SuccessIcon";
function Di(t) {
  const { model: e } = t, r = te(K), s = H(e.cellFillColors$, [], !0), n = H(e.cellTextColors$, [], !0), a = U((o) => {
    e.onFilterCheckToggled(o);
  }, [e]), i = U((o) => {
    e.onFilterCheckToggled(o, !1);
  }, [e]);
  return /* @__PURE__ */ f(
    "div",
    {
      "data-u-comp": "sheets-filter-panel-colors-container",
      className: "univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",
      children: /* @__PURE__ */ I(
        "div",
        {
          "data-u-comp": "sheets-filter-panel",
          className: Re("univer-mt-2 univer-box-border univer-flex univer-h-[300px] univer-flex-grow univer-flex-col univer-gap-4 univer-overflow-auto univer-rounded-md univer-px-2 univer-py-2.5", lt),
          children: [
            s.length > 1 && /* @__PURE__ */ I("div", { children: [
              /* @__PURE__ */ f(
                "div",
                {
                  className: "univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",
                  children: r.t("sheets-filter.panel.filter-by-cell-fill-color")
                }
              ),
              /* @__PURE__ */ f(
                "div",
                {
                  className: "univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",
                  children: s.map((o, l) => /* @__PURE__ */ I(
                    "div",
                    {
                      className: "univer-relative univer-h-6 univer-w-6",
                      onClick: () => a(o),
                      children: [
                        o.color ? /* @__PURE__ */ f(
                          "button",
                          {
                            type: "button",
                            className: Re("univer-box-border univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full univer-border univer-border-solid univer-border-transparent univer-bg-gray-300 univer-transition-shadow hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"),
                            style: { backgroundColor: o.color }
                          }
                        ) : /* @__PURE__ */ f(
                          zt,
                          {
                            className: "univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"
                          }
                        ),
                        o.checked && /* @__PURE__ */ f(Ot, {})
                      ]
                    },
                    `sheets-filter-cell-fill-color-${l}`
                  ))
                }
              )
            ] }),
            n.length > 1 && /* @__PURE__ */ I("div", { children: [
              /* @__PURE__ */ f(
                "div",
                {
                  className: "univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",
                  children: r.t("sheets-filter.panel.filter-by-cell-text-color")
                }
              ),
              /* @__PURE__ */ f(
                "div",
                {
                  className: "univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",
                  children: n.map((o, l) => /* @__PURE__ */ I(
                    "div",
                    {
                      className: "univer-relative univer-h-6 univer-w-6",
                      onClick: () => i(o),
                      children: [
                        /* @__PURE__ */ f(
                          "div",
                          {
                            className: "univer-box-border univer-flex univer-h-full univer-w-full univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-border univer-border-solid univer-border-[rgba(13,13,13,0.06)] univer-p-0.5 hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white dark:!univer-border-[rgba(255,255,255,0.06)]",
                            children: /* @__PURE__ */ f(qt, { style: { color: o.color } })
                          }
                        ),
                        o.checked && /* @__PURE__ */ f(Ot, {})
                      ]
                    },
                    `sheets-filter-cell-text-color-${l}`
                  ))
                }
              )
            ] }),
            s.length <= 1 && n.length <= 1 && /* @__PURE__ */ f(
              "div",
              {
                className: "univer-flex univer-h-full univer-w-full univer-items-center univer-justify-center univer-text-sm univer-text-gray-900 dark:!univer-text-gray-200",
                children: r.t("sheets-filter.panel.filter-by-color-none")
              }
            )
          ]
        }
      )
    }
  );
}
function Ot() {
  return /* @__PURE__ */ f(
    "div",
    {
      className: "univer-absolute -univer-bottom-0.5 -univer-right-0.5 univer-flex univer-h-3 univer-w-3 univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-bg-white",
      children: /* @__PURE__ */ f(
        er,
        {
          className: "univer-h-full univer-w-full univer-font-bold univer-text-[#418F1F]"
        }
      )
    }
  );
}
function Bi(t) {
  var v, _;
  const { model: e } = t, r = te(K), s = H(e.conditionItem$, void 0), n = H(e.filterConditionFormParams$, void 0), a = n != null && n.and ? "AND" : "OR", i = U((S) => {
    e.onConditionFormChange({ and: S === "AND" });
  }, [e]), o = Wi(r), l = U((S) => {
    e.onPrimaryConditionChange(S);
  }, [e]), c = Qi(r), h = U((S) => {
    e.onConditionFormChange(S);
  }, [e]), d = r.t("sheets-filter.panel.input-values-placeholder");
  function C(S, E, N) {
    const b = m.getItemByOperator(S).numOfParameters === 1;
    return /* @__PURE__ */ I(Ct, { children: [
      N === "operator2" && /* @__PURE__ */ I(ni, { value: a, onChange: i, children: [
        /* @__PURE__ */ f(St, { value: "AND", children: r.t("sheets-filter.panel.and") }),
        /* @__PURE__ */ f(St, { value: "OR", children: r.t("sheets-filter.panel.or") })
      ] }),
      /* @__PURE__ */ f(
        gt,
        {
          value: S,
          options: c,
          onChange: (F) => h({ [N]: F })
        }
      ),
      b && /* @__PURE__ */ f("div", { children: /* @__PURE__ */ f(
        Qt,
        {
          className: "univer-mt-2",
          value: E,
          placeholder: d,
          onChange: (F) => h({ [N === "operator1" ? "val1" : "val2"]: F })
        }
      ) })
    ] });
  }
  return /* @__PURE__ */ f(
    "div",
    {
      "data-u-comp": "sheets-filter-panel-conditions-container",
      className: "univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",
      children: s && n && /* @__PURE__ */ I(Ct, { children: [
        /* @__PURE__ */ f(gt, { value: s.operator, options: o, onChange: l }),
        m.getItemByOperator(s.operator).numOfParameters !== 0 ? /* @__PURE__ */ I(
          "div",
          {
            "data-u-comp": "sheets-filter-panel-conditions-container-inner",
            className: Re("univer-mt-2 univer-flex-grow univer-overflow-hidden univer-rounded-md univer-p-2", lt),
            children: [
              s.numOfParameters >= 1 && C(n.operator1, (v = n.val1) != null ? v : "", "operator1"),
              s.numOfParameters >= 2 && C(n.operator2, (_ = n.val2) != null ? _ : "", "operator2"),
              /* @__PURE__ */ I(
                "div",
                {
                  "data-u-comp": "sheets-filter-panel-conditions-desc",
                  className: "univer-mt-2 univer-text-xs univer-text-gray-500",
                  children: [
                    r.t("sheets-filter.panel.?"),
                    /* @__PURE__ */ f("br", {}),
                    r.t("sheets-filter.panel.*")
                  ]
                }
              )
            ]
          }
        ) : null
      ] })
    }
  );
}
function Wi(t) {
  const e = t.getCurrentLocale();
  return at(() => [
    {
      options: [
        { label: t.t(m.NONE.label), value: m.NONE.operator }
      ]
    },
    {
      options: [
        { label: t.t(m.EMPTY.label), value: m.EMPTY.operator },
        { label: t.t(m.NOT_EMPTY.label), value: m.NOT_EMPTY.operator }
      ]
    },
    {
      options: [
        { label: t.t(m.TEXT_CONTAINS.label), value: m.TEXT_CONTAINS.operator },
        { label: t.t(m.DOES_NOT_CONTAIN.label), value: m.DOES_NOT_CONTAIN.operator },
        { label: t.t(m.STARTS_WITH.label), value: m.STARTS_WITH.operator },
        { label: t.t(m.ENDS_WITH.label), value: m.ENDS_WITH.operator },
        { label: t.t(m.EQUALS.label), value: m.EQUALS.operator }
      ]
    },
    {
      options: [
        { label: t.t(m.GREATER_THAN.label), value: m.GREATER_THAN.operator },
        { label: t.t(m.GREATER_THAN_OR_EQUAL.label), value: m.GREATER_THAN_OR_EQUAL.operator },
        { label: t.t(m.LESS_THAN.label), value: m.LESS_THAN.operator },
        { label: t.t(m.LESS_THAN_OR_EQUAL.label), value: m.LESS_THAN_OR_EQUAL.operator },
        { label: t.t(m.EQUAL.label), value: m.EQUAL.operator },
        { label: t.t(m.NOT_EQUAL.label), value: m.NOT_EQUAL.operator },
        { label: t.t(m.BETWEEN.label), value: m.BETWEEN.operator },
        { label: t.t(m.NOT_BETWEEN.label), value: m.NOT_BETWEEN.operator }
      ]
    },
    {
      options: [
        { label: t.t(m.CUSTOM.label), value: m.CUSTOM.operator }
      ]
    }
  ], [e, t]);
}
function Qi(t) {
  const e = t.getCurrentLocale();
  return at(() => m.ALL_CONDITIONS.filter((r) => r.numOfParameters !== 2).map((r) => ({ label: t.t(r.label), value: r.operator })), [e, t]);
}
function Vi(t) {
  const { model: e } = t, r = te(K), s = H(e.searchString$, "", !0), n = H(e.filterItems$, void 0, !0), a = r.t("sheets-filter.panel.filter-only"), i = Xe(n), o = i.checked > 0 && i.unchecked === 0, l = i.checked > 0 && i.unchecked > 0, c = e.treeMapCache, h = U(() => {
    e.onCheckAllToggled(!o);
  }, [e, o]), d = U((v) => {
    e.setSearchString(v);
  }, [e]);
  function C(v) {
    let _ = [];
    return v.forEach((S) => {
      S.checked && _.push(S.key), S.children && (_ = _.concat(C(S.children)));
    }), _;
  }
  return /* @__PURE__ */ I(
    "div",
    {
      "data-u-comp": "sheets-filter-panel-values-container",
      className: "univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",
      children: [
        /* @__PURE__ */ f(
          Qt,
          {
            autoFocus: !0,
            value: s,
            placeholder: r.t("sheets-filter.panel.search-placeholder"),
            onChange: d
          }
        ),
        /* @__PURE__ */ I(
          "div",
          {
            "data-u-comp": "sheets-filter-panel",
            className: Re("univer-mt-2 univer-box-border univer-flex univer-flex-grow univer-flex-col univer-overflow-hidden univer-rounded-md univer-px-2 univer-py-2.5", lt),
            children: [
              /* @__PURE__ */ f(
                "div",
                {
                  "data-u-comp": "sheets-filter-panel-values-item",
                  className: "univer-box-border univer-h-8 univer-w-full univer-py-0.5",
                  children: /* @__PURE__ */ I(
                    "div",
                    {
                      "data-u-comp": "sheets-filter-panel-values-item-inner",
                      className: "univer-box-border univer-flex univer-h-7 univer-items-center univer-rounded-md univer-pb-0 univer-pl-5 univer-pr-0.5 univer-pt-0 univer-text-sm",
                      children: [
                        /* @__PURE__ */ f(
                          oi,
                          {
                            indeterminate: l,
                            disabled: n.length === 0,
                            checked: o,
                            onChange: h
                          }
                        ),
                        /* @__PURE__ */ f(
                          "span",
                          {
                            "data-u-comp": "sheets-filter-panel-values-item-text",
                            className: "univer-mx-1 univer-inline-block univer-flex-shrink univer-overflow-hidden univer-text-ellipsis univer-whitespace-nowrap univer-text-gray-900 dark:!univer-text-white",
                            children: `${r.t("sheets-filter.panel.select-all")}`
                          }
                        ),
                        /* @__PURE__ */ f(
                          "span",
                          {
                            "data-u-comp": "sheets-filter-panel-values-item-count",
                            className: "univer-text-gray-400 dark:!univer-text-gray-500",
                            children: `(${i.checked}/${i.checked + i.unchecked})`
                          }
                        )
                      ]
                    }
                  )
                }
              ),
              /* @__PURE__ */ f("div", { "data-u-comp": "sheets-filter-panel-values-virtual", className: "univer-flex-grow", children: /* @__PURE__ */ f(
                li,
                {
                  data: n,
                  defaultExpandAll: !1,
                  valueGroup: C(n),
                  onChange: (v) => {
                    e.onFilterCheckToggled(v);
                  },
                  defaultCache: c,
                  itemHeight: 28,
                  treeNodeClassName: `
                          univer-pr-2 univer-border-box univer-max-w-[245px] univer-rounded-md
                          [&:hover_a]:univer-inline-block
                          hover:univer-bg-gray-50 univer-h-full
                          univer-text-gray-900 dark:hover:!univer-bg-gray-900
                          dark:!univer-text-white
                        `,
                  attachRender: (v) => /* @__PURE__ */ I(
                    "div",
                    {
                      className: "univer-ml-1 univer-flex univer-h-5 univer-flex-1 univer-cursor-pointer univer-items-center univer-justify-between univer-text-sm univer-text-primary-500",
                      children: [
                        /* @__PURE__ */ f(
                          "span",
                          {
                            "data-u-comp": "sheets-filter-panel-values-item-count",
                            className: "univer-text-gray-400 dark:!univer-text-gray-500",
                            children: `(${v.count})`
                          }
                        ),
                        /* @__PURE__ */ f(
                          "a",
                          {
                            className: "univer-box-border univer-hidden univer-h-4 univer-whitespace-nowrap univer-px-1.5",
                            onClick: () => {
                              const _ = [];
                              v.children ? v.children.forEach((S) => {
                                S.children ? S.children.forEach((E) => {
                                  _.push(E.key);
                                }) : _.push(S.key);
                              }) : _.push(v.key), e.onFilterOnly(_);
                            },
                            children: a
                          }
                        )
                      ]
                    }
                  )
                }
              ) })
            ]
          }
        )
      ]
    }
  );
}
function ji() {
  var E;
  const t = te(Z), e = te(K), r = te(j), s = H(t.filterBy$, void 0, !0), n = H(t.filterByModel$, void 0, !1), a = H(() => (n == null ? void 0 : n.canApply$) || me(!1), void 0, !1, [n]), i = Gi(e), o = !H(t.hasCriteria$), l = U((N) => {
    r.executeCommand(Yt.id, { filterBy: N });
  }, [r]), c = U(async () => {
    await (n == null ? void 0 : n.clear()), r.executeCommand(Se.id);
  }, [n, r]), h = U(() => {
    r.executeCommand(Se.id);
  }, [r]), d = U(async () => {
    await (n == null ? void 0 : n.apply()), r.executeCommand(Se.id);
  }, [n, r]), v = (E = te(X).activeFilterModel) == null ? void 0 : E.getRange(), _ = t.col, S = wr(Or.FILTER_PANEL_EMBED_POINT);
  return /* @__PURE__ */ I(
    "div",
    {
      "data-u-comp": "sheets-filter-panel",
      className: "univer-box-border univer-flex univer-max-h-[500px] univer-w-[400px] univer-flex-col univer-rounded-lg univer-bg-white univer-p-4 univer-shadow-lg dark:!univer-border-gray-600 dark:!univer-bg-gray-700",
      children: [
        /* @__PURE__ */ f(
          Lr,
          {
            components: S,
            sharedProps: { range: v, colIndex: _, onClose: h }
          }
        ),
        /* @__PURE__ */ f("div", { className: "univer-mb-1 univer-flex-shrink-0 univer-flex-grow-0", children: /* @__PURE__ */ f(
          ai,
          {
            value: s,
            items: i,
            onChange: (N) => l(N)
          }
        ) }),
        n ? /* @__PURE__ */ f(
          "div",
          {
            "data-u-comp": "sheets-filter-panel-content",
            className: "univer-flex-shrink univer-flex-grow univer-pt-2",
            children: s === A.VALUES ? /* @__PURE__ */ f(Vi, { model: n }) : s === A.COLORS ? /* @__PURE__ */ f(Di, { model: n }) : /* @__PURE__ */ f(Bi, { model: n })
          }
        ) : /* @__PURE__ */ f("div", { className: "univer-flex-1" }),
        /* @__PURE__ */ I(
          "div",
          {
            "data-u-comp": "sheets-filter-panel-footer",
            className: "univer-mt-4 univer-inline-flex univer-flex-shrink-0 univer-flex-grow-0 univer-flex-nowrap univer-justify-between univer-overflow-hidden",
            children: [
              /* @__PURE__ */ f(je, { variant: "link", onClick: c, disabled: o, children: e.t("sheets-filter.panel.clear-filter") }),
              /* @__PURE__ */ I("span", { className: "univer-flex univer-gap-2", children: [
                /* @__PURE__ */ f(je, { variant: "default", onClick: h, children: e.t("sheets-filter.panel.cancel") }),
                /* @__PURE__ */ f(je, { disabled: !a, variant: "primary", onClick: d, children: e.t("sheets-filter.panel.confirm") })
              ] })
            ]
          }
        )
      ]
    }
  );
}
function Gi(t) {
  const e = t.getCurrentLocale();
  return at(() => [
    { label: t.t("sheets-filter.panel.by-values"), value: A.VALUES },
    { label: t.t("sheets-filter.panel.by-colors"), value: A.COLORS },
    { label: t.t("sheets-filter.panel.by-conditions"), value: A.CONDITIONS }
  ], [e, t]);
}
function Yi(t) {
  const e = t.get(X);
  return {
    id: Ee.id,
    type: nt.BUTTON_SELECTOR,
    icon: "FilterIcon",
    tooltip: "sheets-filter.toolbar.smart-toggle-filter-tooltip",
    hidden$: st(t, ie.UNIVER_SHEET),
    activated$: e.activeFilterModel$.pipe(ce((r) => !!r)),
    disabled$: Ir(
      t,
      br(
        t,
        {
          worksheetTypes: [pe, ve],
          rangeTypes: [_e]
        }
      )
    )
  };
}
function Zi(t) {
  const e = t.get(X);
  return {
    id: it.id,
    type: nt.BUTTON,
    title: "sheets-filter.toolbar.clear-filter-criteria",
    hidden$: st(t, ie.UNIVER_SHEET),
    disabled$: e.activeFilterModel$.pipe(ot((r) => {
      var s;
      return (s = r == null ? void 0 : r.hasCriteria$.pipe(ce((n) => !n))) != null ? s : me(!0);
    }))
  };
}
function Ki(t) {
  const e = t.get(X);
  return {
    id: rt.id,
    type: nt.BUTTON,
    title: "sheets-filter.toolbar.re-calc-filter-conditions",
    hidden$: st(t, ie.UNIVER_SHEET),
    disabled$: e.activeFilterModel$.pipe(ot((r) => {
      var s;
      return (s = r == null ? void 0 : r.hasCriteria$.pipe(ce((n) => !n))) != null ? s : me(!0);
    }))
  };
}
const Xi = {
  [$r.ORGANIZATION]: {
    [Ee.id]: {
      order: 2,
      menuItemFactory: Yi,
      [it.id]: {
        order: 0,
        menuItemFactory: Zi
      },
      [rt.id]: {
        order: 1,
        menuItemFactory: Ki
      }
    }
  }
}, qi = {
  id: Ee.id,
  binding: Mr.L | pt.CTRL_COMMAND | pt.SHIFT,
  description: "sheets-filter.shortcut.smart-toggle-filter",
  preconditions: Rr,
  group: "4_sheet-edit"
};
var zi = Object.getOwnPropertyDescriptor, Ji = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? zi(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, P = (t, e) => (r, s) => e(r, s, t);
const It = "FILTER_PANEL_POPUP";
let Ue = class extends Te {
  constructor(e, r, s, n, a, i, o, l, c, h, d, C, v) {
    super(v, C);
    p(this, "_popupDisposable");
    this._injector = e, this._componentManager = r, this._sheetsFilterPanelService = s, this._sheetCanvasPopupService = n, this._sheetsFilterService = a, this._localeService = i, this._shortcutService = o, this._commandService = l, this._menuManagerService = c, this._contextService = h, this._messageService = d, this._initCommands(), this._initShortcuts(), this._initMenuItems(), this._initUI();
  }
  dispose() {
    super.dispose(), this._closeFilterPopup();
  }
  _initShortcuts() {
    [
      qi
    ].forEach((e) => {
      this.disposeWithMe(this._shortcutService.registerShortcut(e));
    });
  }
  _initCommands() {
    [
      Ee,
      gr,
      Sr,
      Q,
      it,
      rt,
      Yt,
      $e,
      Se
    ].forEach((e) => {
      this.disposeWithMe(this._commandService.registerCommand(e));
    });
  }
  _initMenuItems() {
    this._menuManagerService.mergeMenu(Xi);
  }
  _initUI() {
    [
      [It, ji],
      ["FilterIcon", Jt]
    ].forEach(([e, r]) => {
      this.disposeWithMe(
        this._componentManager.register(e, r)
      );
    }), this.disposeWithMe(this._contextService.subscribeContextValue$(ue).pipe(ri()).subscribe((e) => {
      e ? this._openFilterPopup() : this._closeFilterPopup();
    })), this.disposeWithMe(this._sheetsFilterService.errorMsg$.subscribe((e) => {
      e && this._messageService.show({
        type: ci.Error,
        content: this._localeService.t(e)
      });
    }));
  }
  _openFilterPopup() {
    const e = this._sheetsFilterPanelService.filterModel;
    if (!e)
      throw new Error("[SheetsFilterUIController]: no filter model when opening filter popup!");
    const r = e.getRange(), s = this._sheetsFilterPanelService.col, { startRow: n } = r;
    this._popupDisposable = this._sheetCanvasPopupService.attachPopupToCell(n, s, {
      componentKey: It,
      direction: "horizontal",
      onClickOutside: () => this._commandService.syncExecuteCommand(Se.id),
      offset: [5, 0]
    });
  }
  _closeFilterPopup() {
    var e;
    (e = this._popupDisposable) == null || e.dispose(), this._popupDisposable = null;
  }
};
Ue = Ji([
  P(0, T(re)),
  P(1, T(xr)),
  P(2, T(Z)),
  P(3, T(Ar)),
  P(4, T(X)),
  P(5, T(K)),
  P(6, Ur),
  P(7, j),
  P(8, kr),
  P(9, Be),
  P(10, Hr),
  P(11, T(kt)),
  P(12, Ht)
], Ue);
var es = Object.defineProperty, ts = Object.getOwnPropertyDescriptor, rs = (t, e, r) => e in t ? es(t, e, { enumerable: !0, configurable: !0, writable: !0, value: r }) : t[e] = r, is = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? ts(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, Ke = (t, e) => (r, s) => e(r, s, t), tr = (t, e, r) => rs(t, typeof e != "symbol" ? e + "" : e, r);
const ss = "SHEET_FILTER_UI_PLUGIN";
let ke = class extends tt {
  constructor(t = Me, e, r, s) {
    super(), this._config = t, this._injector = e, this._configService = r, this._rpcChannelService = s;
    const { menu: n, ...a } = xt(
      {},
      Me,
      this._config
    );
    n && this._configService.setConfig("menu", n, { merge: !0 }), this._configService.setConfig(Zt, a);
  }
  onStarting() {
    dr(this._injector, [
      [Z],
      [he],
      [Ue]
    ]), this._config.useRemoteFilterValuesGenerator && this._rpcChannelService && this._injector.add([Ae, {
      useFactory: () => ii(
        this._rpcChannelService.requestChannel(ct)
      )
    }]);
  }
  onReady() {
    ft(this._injector, [
      [he]
    ]);
  }
  onRendered() {
    ft(this._injector, [
      [Ue]
    ]);
  }
};
tr(ke, "type", ie.UNIVER_SHEET);
tr(ke, "pluginName", ss);
ke = is([
  $t(Ut),
  Ke(1, T(re)),
  Ke(2, Mt),
  Ke(3, hr(Wt))
], ke);
var ns = Object.getOwnPropertyDescriptor, os = (t, e, r, s) => {
  for (var n = s > 1 ? void 0 : s ? ns(e, r) : e, a = t.length - 1, i; a >= 0; a--)
    (i = t[a]) && (n = i(n) || n);
  return n;
}, bt = (t, e) => (r, s) => e(r, s, t), Ie;
let Rt = (Ie = class extends tt {
  constructor(t, e, r) {
    super(), this._config = t, this._injector = e, this._rpcChannelService = r;
  }
  onStarting() {
    [
      [Ae, { useClass: qe }]
    ].forEach((t) => this._injector.add(t));
  }
  onReady() {
    this._rpcChannelService.registerChannel(
      ct,
      si(this._injector.get(Ae))
    );
  }
}, p(Ie, "type", ie.UNIVER_SHEET), p(Ie, "pluginName", "SHEET_FILTER_UI_WORKER_PLUGIN"), Ie);
Rt = os([
  bt(1, T(re)),
  bt(2, Wt)
], Rt);
export {
  Yt as ChangeFilterByOperation,
  Se as CloseFilterPanelOperation,
  $e as OpenFilterPanelOperation,
  xe as UniverSheetsFilterMobileUIPlugin,
  ke as UniverSheetsFilterUIPlugin,
  Rt as UniverSheetsFilterUIWorkerPlugin
};

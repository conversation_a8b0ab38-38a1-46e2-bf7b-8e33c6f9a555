(function(_,u){typeof exports=="object"&&typeof module<"u"?u(exports,require("@univerjs/core"),require("@univerjs/sheets-filter"),require("@univerjs/sheets-ui"),require("@univerjs/ui"),require("@univerjs/engine-render"),require("@univerjs/sheets"),require("rxjs"),require("@univerjs/rpc"),require("@univerjs/design"),require("react/jsx-runtime"),require("react")):typeof define=="function"&&define.amd?define(["exports","@univerjs/core","@univerjs/sheets-filter","@univerjs/sheets-ui","@univerjs/ui","@univerjs/engine-render","@univerjs/sheets","rxjs","@univerjs/rpc","@univerjs/design","react/jsx-runtime","react"],u):(_=typeof globalThis<"u"?globalThis:_||self,u(_.UniverSheetsFilterUi={},_.UniverCore,_.UniverSheetsFilter,_.UniverSheetsUi,_.UniverUi,_.UniverEngineRender,_.UniverSheets,_.rxjs,_.UniverRpc,_.UniverDesign,_.React,_.React))})(this,function(_,u,c,$,E,K,F,g,pe,N,d,y){"use strict";var rr=Object.defineProperty;var ir=(_,u,c)=>u in _?rr(_,u,{enumerable:!0,configurable:!0,writable:!0,value:c}):_[u]=c;var v=(_,u,c)=>ir(_,typeof u!="symbol"?u+"":u,c);var Ee;var w=(t=>(t[t.FIRST=0]="FIRST",t[t.SECOND=1]="SECOND",t))(w||{}),T=(t=>(t.NONE="none",t.STARTS_WITH="startsWith",t.DOES_NOT_START_WITH="doesNotStartWith",t.ENDS_WITH="endsWith",t.DOES_NOT_END_WITH="doesNotEndWith",t.CONTAINS="contains",t.DOES_NOT_CONTAIN="doesNotContain",t.EQUALS="equals",t.NOT_EQUALS="notEquals",t.EMPTY="empty",t.NOT_EMPTY="notEmpty",t.BETWEEN="between",t.NOT_BETWEEN="notBetween",t.CUSTOM="custom",t))(T||{}),f;(t=>{t.NONE={label:"sheets-filter.conditions.none",operator:T.NONE,order:w.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.NONE]: should not have initial form params!")},testMappingParams:i=>i.operator1===T.NONE,mapToFilterColumn:()=>null,testMappingFilterColumn:i=>!i.customFilters&&!i.filters?{}:!1},t.EMPTY={label:"sheets-filter.conditions.empty",operator:T.EMPTY,order:w.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.EMPTY]: should not have initial form params!")},testMappingParams:({operator1:i})=>i===T.EMPTY,mapToFilterColumn:()=>({customFilters:{customFilters:[{val:""}]}}),testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.val===""&&s.operator===void 0?{operator1:T.EMPTY}:!1}},t.NOT_EMPTY={label:"sheets-filter.conditions.not-empty",operator:T.NOT_EMPTY,order:w.SECOND,numOfParameters:0,getDefaultFormParams:()=>{throw new Error("[FilterConditionItems.NOT_EMPTY]: should not have initial form params!")},testMappingParams:({operator1:i})=>i===T.NOT_EMPTY,mapToFilterColumn:()=>({customFilters:{customFilters:[{val:"",operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.val===" "&&s.operator===c.CustomFilterOperator.NOT_EQUALS?{operator1:T.NOT_EMPTY}:!1}},t.TEXT_CONTAINS={label:"sheets-filter.conditions.text-contains",operator:T.CONTAINS,order:w.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:T.CONTAINS,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===T.CONTAINS},mapToFilterColumn:i=>{const{val1:s}=i;return s===""?null:{customFilters:{customFilters:[{val:`*${s}*`}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0],a=s.val.toString();return!s.operator&&a.startsWith("*")&&a.endsWith("*")?{operator1:T.CONTAINS,val1:a.slice(1,-1)}:!1}},t.DOES_NOT_CONTAIN={label:"sheets-filter.conditions.does-not-contain",operator:T.DOES_NOT_CONTAIN,order:w.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:T.DOES_NOT_CONTAIN,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`*${i.val1}*`,operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingParams:i=>{const[s]=H(i);return s===T.DOES_NOT_CONTAIN},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0],a=s.val.toString();return s.operator===c.CustomFilterOperator.NOT_EQUALS&&a.startsWith("*")&&a.endsWith("*")?{operator1:T.DOES_NOT_CONTAIN,val1:a.slice(1,-1)}:!1}},t.STARTS_WITH={label:"sheets-filter.conditions.starts-with",operator:T.STARTS_WITH,order:w.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:T.STARTS_WITH,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`${i.val1}*`}]}}),testMappingParams:i=>{const[s]=H(i);return s===T.STARTS_WITH},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0],a=s.val.toString();return!s.operator&&a.endsWith("*")&&!a.startsWith("*")?{operator1:T.STARTS_WITH,val1:a.slice(0,-1)}:!1}},t.ENDS_WITH={label:"sheets-filter.conditions.ends-with",operator:T.ENDS_WITH,order:w.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:T.ENDS_WITH,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:`*${i.val1}`}]}}),testMappingParams:i=>{const[s]=H(i);return s===T.ENDS_WITH},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0],a=s.val.toString();return!s.operator&&a.startsWith("*")&&!a.endsWith("*")?{operator1:T.ENDS_WITH,val1:a.slice(1)}:!1}},t.EQUALS={label:"sheets-filter.conditions.equals",operator:T.EQUALS,order:w.FIRST,numOfParameters:1,getDefaultFormParams:()=>({operator1:T.EQUALS,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===T.EQUALS},mapToFilterColumn:i=>{const{val1:s}=i;return s===""?null:{customFilters:{customFilters:[{val:s}]}}},testMappingFilterColumn:i=>{var s,a,h;return((a=(s=i.filters)==null?void 0:s.filters)==null?void 0:a.length)===1?{operator1:T.EQUALS,val1:""}:((h=i.customFilters)==null?void 0:h.customFilters.length)===1&&!i.customFilters.customFilters[0].operator?{operator1:T.EQUALS,val1:i.customFilters.customFilters[0].val.toString()}:!1}},t.GREATER_THAN={label:"sheets-filter.conditions.greater-than",operator:c.CustomFilterOperator.GREATER_THAN,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.GREATER_THAN,val1:""}),mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.GREATER_THAN}]}}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.GREATER_THAN},testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.GREATER_THAN?!1:{operator1:c.CustomFilterOperator.GREATER_THAN,val1:s.val.toString()}}},t.GREATER_THAN_OR_EQUAL={label:"sheets-filter.conditions.greater-than-or-equal",operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL}]}}),testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.GREATER_THAN_OR_EQUAL?!1:{operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:s.val.toString()}}},t.LESS_THAN={label:"sheets-filter.conditions.less-than",operator:c.CustomFilterOperator.LESS_THAN,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.LESS_THAN},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.LESS_THAN}]}}),testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.LESS_THAN?!1:{operator1:c.CustomFilterOperator.LESS_THAN,val1:s.val.toString()}}},t.LESS_THAN_OR_EQUAL={label:"sheets-filter.conditions.less-than-or-equal",operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.LESS_THAN_OR_EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL}]}}),testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.LESS_THAN_OR_EQUAL?!1:{operator1:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val1:s.val.toString()}}},t.EQUAL={label:"sheets-filter.conditions.equal",operator:c.CustomFilterOperator.EQUAL,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.EQUAL,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.EQUAL},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.EQUAL}]}}),testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.EQUAL?!1:{operator1:c.CustomFilterOperator.EQUAL,val1:s.val.toString()}}},t.NOT_EQUAL={label:"sheets-filter.conditions.not-equal",operator:c.CustomFilterOperator.NOT_EQUALS,numOfParameters:1,order:w.FIRST,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.NOT_EQUALS,val1:""}),testMappingParams:i=>{const[s]=H(i);return s===c.CustomFilterOperator.NOT_EQUALS},mapToFilterColumn:i=>({customFilters:{customFilters:[{val:i.val1,operator:c.CustomFilterOperator.NOT_EQUALS}]}}),testMappingFilterColumn:i=>{var a;if(((a=i.customFilters)==null?void 0:a.customFilters.length)!==1)return!1;const s=i.customFilters.customFilters[0];return s.operator!==c.CustomFilterOperator.NOT_EQUALS?!1:{operator1:c.CustomFilterOperator.NOT_EQUALS,val1:s.val.toString()}}},t.BETWEEN={label:"sheets-filter.conditions.between",operator:T.BETWEEN,order:w.SECOND,numOfParameters:2,getDefaultFormParams:()=>({and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:"",operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:""}),testMappingParams:i=>{const{and:s,operator1:a,operator2:h}=i;if(!s)return!1;const m=[a,h];return m.includes(c.CustomFilterOperator.GREATER_THAN_OR_EQUAL)&&m.includes(c.CustomFilterOperator.LESS_THAN_OR_EQUAL)},mapToFilterColumn:i=>{const{val1:s,val2:a,operator1:h}=i,m=h===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL;return{customFilters:{and:u.BooleanNumber.TRUE,customFilters:[{val:m?s:a,operator:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL},{val:m?a:s,operator:c.CustomFilterOperator.LESS_THAN_OR_EQUAL}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const[s,a]=i.customFilters.customFilters;return s.operator===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL&&a.operator===c.CustomFilterOperator.LESS_THAN_OR_EQUAL&&i.customFilters.and?{and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:s.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:a.val.toString()}:a.operator===c.CustomFilterOperator.GREATER_THAN_OR_EQUAL&&s.operator===c.CustomFilterOperator.LESS_THAN_OR_EQUAL&&i.customFilters.and?{and:!0,operator1:c.CustomFilterOperator.GREATER_THAN_OR_EQUAL,val1:a.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN_OR_EQUAL,val2:s.val.toLocaleString()}:!1}},t.NOT_BETWEEN={label:"sheets-filter.conditions.not-between",operator:T.NOT_BETWEEN,order:w.SECOND,numOfParameters:2,getDefaultFormParams:()=>({operator1:c.CustomFilterOperator.LESS_THAN,val1:"",operator2:c.CustomFilterOperator.GREATER_THAN,val2:""}),testMappingParams:i=>{const{and:s,operator1:a,operator2:h}=i;if(s)return!1;const m=[a,h];return m.includes(c.CustomFilterOperator.GREATER_THAN)&&m.includes(c.CustomFilterOperator.LESS_THAN)},mapToFilterColumn:i=>{const{val1:s,val2:a,operator1:h}=i,m=h===c.CustomFilterOperator.GREATER_THAN;return{customFilters:{customFilters:[{val:m?s:a,operator:c.CustomFilterOperator.GREATER_THAN},{val:m?a:s,operator:c.CustomFilterOperator.LESS_THAN}]}}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const[s,a]=i.customFilters.customFilters;return s.operator===c.CustomFilterOperator.LESS_THAN&&a.operator===c.CustomFilterOperator.GREATER_THAN&&!i.customFilters.and?{operator1:c.CustomFilterOperator.LESS_THAN,val1:s.val.toString(),operator2:c.CustomFilterOperator.GREATER_THAN,val2:a.val.toString()}:a.operator===c.CustomFilterOperator.LESS_THAN&&s.operator===c.CustomFilterOperator.GREATER_THAN&&!i.customFilters.and?{operator1:c.CustomFilterOperator.GREATER_THAN,val1:a.val.toString(),operator2:c.CustomFilterOperator.LESS_THAN,val2:s.val.toLocaleString()}:!1}},t.CUSTOM={label:"sheets-filter.conditions.custom",operator:T.CUSTOM,order:w.SECOND,numOfParameters:2,getDefaultFormParams:()=>({operator1:T.NONE,val1:"",operator2:T.NONE,val2:""}),testMappingParams:()=>!0,mapToFilterColumn:i=>{const{and:s,val1:a,val2:h,operator1:m,operator2:p}=i;function I(L,A){for(const M of t.ALL_CONDITIONS)if(M.operator===L)return M.mapToFilterColumn({val1:A,operator1:L})}const S=!m||m===t.NONE.operator,C=!p||p===t.NONE.operator;if(S&&C)return t.NONE.mapToFilterColumn({});if(S)return I(p,h);if(C)return I(m,a);const O=I(m,a),b=I(p,h),P={customFilters:[O.customFilters.customFilters[0],b.customFilters.customFilters[0]]};return s&&(P.and=u.BooleanNumber.TRUE),{customFilters:P}},testMappingFilterColumn:i=>{var h;if(((h=i.customFilters)==null?void 0:h.customFilters.length)!==2)return!1;const s=i.customFilters.customFilters.map(m=>l({customFilters:{customFilters:[m]}})),a={operator1:s[0][0].operator,val1:s[0][1].val1,operator2:s[1][0].operator,val2:s[1][1].val1};return i.customFilters.and&&(a.and=!0),a}},t.ALL_CONDITIONS=[t.NONE,t.EMPTY,t.NOT_EMPTY,t.TEXT_CONTAINS,t.DOES_NOT_CONTAIN,t.STARTS_WITH,t.ENDS_WITH,t.EQUALS,t.GREATER_THAN,t.GREATER_THAN_OR_EQUAL,t.LESS_THAN,t.LESS_THAN_OR_EQUAL,t.EQUAL,t.NOT_EQUAL,t.BETWEEN,t.NOT_BETWEEN,t.CUSTOM];function e(i){const s=t.ALL_CONDITIONS.find(a=>a.operator===i);if(!s)throw new Error(`[SheetsFilter]: no condition item found for operator: ${i}`);return s}t.getItemByOperator=e;function r(i,s){for(const a of t.ALL_CONDITIONS.filter(h=>h.numOfParameters===s))if(a.numOfParameters!==0&&a.testMappingParams(i))return a;for(const a of t.ALL_CONDITIONS)if(a.testMappingParams(i))return a;throw new Error("[SheetsFilter]: no condition item can be mapped from the filter map params!")}t.testMappingParams=r;function n(i){const s=t.ALL_CONDITIONS.find(a=>a.operator===i);return(s==null?void 0:s.numOfParameters)===0?{operator1:s.operator}:s.getDefaultFormParams()}t.getInitialFormParams=n;function o(i,s){return i.mapToFilterColumn(s)}t.mapToFilterColumn=o;function l(i){if(!i)return[t.NONE,{}];for(const s of t.ALL_CONDITIONS){const a=s.testMappingFilterColumn(i);if(a)return[s,a]}return[t.NONE,{}]}t.testMappingFilterColumn=l})(f||(f={}));function H(t){const{operator1:e,operator2:r,val1:n,val2:o}=t;if(e&&r)throw new Error("Both operator1 and operator2 are set!");if(!e&&!r)throw new Error("Neither operator1 and operator2 and both not set!");return e?[e,n]:[r,o]}function Ie(t){const e=[],r=[];let n=0,o=0;function l(i){i.leaf&&(i.checked?(e.push(i),n+=i.count):(r.push(i),o+=i.count)),i.children&&i.children.forEach(l)}return t.forEach(l),{checkedItems:e,uncheckedItems:r,checked:n,unchecked:o}}var it=Object.getOwnPropertyDescriptor,nt=(t,e,r,n)=>{for(var o=n>1?void 0:n?it(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},ye=(t,e)=>(r,n)=>e(r,n,t);const Fe="sheets-filter.generate-filter-values.service",fe=u.createIdentifier(Fe),ot=["yyyy-mm-dd","yyyy-mm-dd;@","yyyy/mm/dd;@","yyyy/mm/dd hh:mm","yyyy-m-d am/pm h:mm","yyyy-MM-dd","yyyy/MM/dd","yyyy/mm/dd",'yyyy"年"MM"月"dd"日"',"MM-dd",'M"月"d"日"',"MM-dd A/P hh:mm"];let Ne=class extends u.Disposable{constructor(t,e,r){super(),this._localeService=t,this._univerInstanceService=e,this._logService=r}async getFilterValues(t){var p;const{unitId:e,subUnitId:r,filteredOutRowsByOtherColumns:n,filterColumn:o,filters:l,blankChecked:i,iterateRange:s,alreadyChecked:a}=t,h=this._univerInstanceService.getUnit(e),m=(p=this._univerInstanceService.getUnit(e))==null?void 0:p.getSheetBySheetId(r);return!h||!m?[]:(this._logService.debug("[SheetsGenerateFilterValuesService]","getFilterValues for",{unitId:e,subUnitId:r}),ke(l,this._localeService,s,m,new Set(n),o,new Set(a.map(String)),i,h.getStyles()))}};Ne=nt([ye(0,u.Inject(u.LocaleService)),ye(1,u.IUniverInstanceService),ye(2,u.ILogService)],Ne);function ke(t,e,r,n,o,l,i,s,a){var P,L,A,M,B,X,ne,oe,V,x;const h=new Map,m=new Map,p="yyyy-mm-dd",I=new Set(ot),S="empty",C=!t&&((l==null?void 0:l.filterBy)===c.FilterBy.COLORS||(l==null?void 0:l.filterBy)===c.FilterBy.CONDITIONS)&&((P=l.filteredOutRows)==null?void 0:P.size);let O=0;for(const D of n.iterateByColumn(r,!1,!1)){const{row:Jt,rowSpan:rt=1}=D;let se=0;for(;se<rt;){const er=Jt+se;if(o.has(er)){se++;continue}const z=D!=null&&D.value?u.extractPureTextFromCell(D.value):"";if(!z){O+=1,se+=rt;continue}const Oe=(L=D.value)!=null&&L.v&&!D.value.p?(B=(M=a.get((A=D.value)==null?void 0:A.s))==null?void 0:M.n)==null?void 0:B.pattern:"",tr=Oe&&u.numfmt.getFormatInfo(Oe).isDate;if(Oe&&tr&&I.has(Oe)){const q=(X=n.getCellRaw(D.row,D.col))==null?void 0:X.v;if(!q){se++;continue}const ae=u.numfmt.format(p,q),[R,W,me]=ae.split("-").map(Number);let J=h.get(`${R}`);J||(J={title:`${R}`,key:`${R}`,children:[],count:0,leaf:!1,checked:!1},h.set(`${R}`,J),m.set(`${R}`,[`${R}`]));let Q=(ne=J.children)==null?void 0:ne.find(Ue=>Ue.key===`${R}-${W}`);Q||(Q={title:e.t(`sheets-filter.date.${W}`),key:`${R}-${W}`,children:[],count:0,leaf:!1,checked:!1},(oe=J.children)==null||oe.push(Q),m.set(`${R}-${W}`,[`${R}`,`${R}-${W}`]));const $e=(V=Q==null?void 0:Q.children)==null?void 0:V.find(Ue=>Ue.key===`${R}-${W}-${me}`);$e?($e.originValues.add(z),$e.count++,Q.count++,J.count++):((x=Q.children)==null||x.push({title:`${me}`,key:`${R}-${W}-${me}`,count:1,originValues:new Set([z]),leaf:!0,checked:C?!1:i.size?i.has(z):!s}),Q.count++,J.count++,m.set(`${R}-${W}-${me}`,[`${R}`,`${R}-${W}`,`${R}-${W}-${me}`]))}else{const q=z;let ae=h.get(q);ae?ae.count++:(ae={title:z,leaf:!0,checked:C?!1:i.size?i.has(z):!s,key:q,count:1},h.set(q,ae),m.set(q,[q]))}se++}}const b=C?!1:t?s:!0;if(O>0){const D={title:e.t("sheets-filter.panel.empty"),count:O,leaf:!0,checked:b,key:S};h.set("empty",D),m.set("empty",[S])}return{filterTreeItems:st(Array.from(h.values())),filterTreeMapCache:m}}function st(t){return Array.from(t).sort((e,r)=>e.children&&!r.children?-1:!e.children&&r.children?1:at(e.title,r.title)).map(e=>(e.children&&e.children.sort((r,n)=>{const o=Number.parseInt(r.key.split("-")[1],10),l=Number.parseInt(n.key.split("-")[1],10);return o-l}).forEach(r=>{r.children&&r.children.sort((n,o)=>{const l=Number.parseInt(n.key.split("-")[2],10),i=Number.parseInt(o.key.split("-")[2],10);return l-i})}),e))}const Be=t=>!Number.isNaN(Number(t))&&!Number.isNaN(Number.parseFloat(t));function at(t,e){const r=Be(t),n=Be(e);return r&&n?Number.parseFloat(t)-Number.parseFloat(e):r&&!n?-1:!r&&n?1:t.localeCompare(e)}function be(t,e){for(const r of t){if(r.key===e)return r;if(r.children){const n=be(r.children,e);if(n)return n}}return null}function xe(t){return t.leaf?t.checked:t.children?t.children.every(e=>xe(e)):!0}function le(t,e){t.leaf&&(e!==void 0?t.checked=e:t.checked=!t.checked),t.children&&t.children.forEach(r=>le(r,e))}function De(t,e){const r=[];return t.forEach(n=>{const o=n.originValues?e.some(s=>Array.from(n.originValues).some(a=>a.toLowerCase().includes(s.toLowerCase()))):!1,l=!o&&e.some(s=>n.title.toLowerCase().includes(s.toLowerCase()));if(o||l)r.push({...n});else if(n.children){const s=De(n.children,e);if(s.length>0){const a=s.reduce((h,m)=>h+m.count,0);r.push({...n,count:a,children:s})}}}),r}var lt=Object.getOwnPropertyDescriptor,ve=(t,e,r,n)=>{for(var o=n>1?void 0:n?lt(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},ce=(t,e)=>(r,n)=>e(r,n,t);u.createIdentifier("sheets-filter-ui.sheets-filter-panel.service");let G=class extends u.Disposable{constructor(e,r){super();v(this,"_filterBy$",new g.BehaviorSubject(c.FilterBy.VALUES));v(this,"filterBy$",this._filterBy$.asObservable());v(this,"_filterByModel$",new g.ReplaySubject(1));v(this,"filterByModel$",this._filterByModel$.asObservable());v(this,"_filterByModel",null);v(this,"_hasCriteria$",new g.BehaviorSubject(!1));v(this,"hasCriteria$",this._hasCriteria$.asObservable());v(this,"_filterModel",null);v(this,"_col$",new g.BehaviorSubject(-1));v(this,"col$",this._col$.asObservable());v(this,"_filterHeaderListener",null);this._injector=e,this._refRangeService=r}get filterBy(){return this._filterBy$.getValue()}get filterByModel(){return this._filterByModel}set filterByModel(e){this._filterByModel=e,this._filterByModel$.next(e)}get filterModel(){return this._filterModel}get col(){return this._col$.getValue()}dispose(){this._filterBy$.complete(),this._filterByModel$.complete(),this._hasCriteria$.complete()}setupCol(e,r){this.terminate(),this._filterModel=e,this._col$.next(r);const n=e.getFilterColumn(r);if(n){const o=n.getColumnData();if(o.customFilters){this._hasCriteria$.next(!0),this._setupByConditions(e,r);return}if(o.colorFilters){this._hasCriteria$.next(!0),this._setupByColors(e,r);return}if(o.filters){this._hasCriteria$.next(!0),this._setupByValues(e,r);return}this._hasCriteria$.next(!1),this._setupByValues(e,r);return}this._hasCriteria$.next(!1),this._setupByValues(e,r)}changeFilterBy(e){if(!this._filterModel||this.col===-1)return!1;switch(e){case c.FilterBy.VALUES:this._setupByValues(this._filterModel,this.col);break;case c.FilterBy.COLORS:this._setupByColors(this._filterModel,this.col);break;case c.FilterBy.CONDITIONS:this._setupByConditions(this._filterModel,this.col);break}return!0}terminate(){return this._filterModel=null,this._col$.next(-1),this._disposeFilterHeaderChangeListener(),!0}_disposeFilterHeaderChangeListener(){var e;(e=this._filterHeaderListener)==null||e.dispose(),this._filterHeaderListener=null}_listenToFilterHeaderChange(e,r){this._disposeFilterHeaderChangeListener();const n=e.unitId,o=e.subUnitId,l=e.getRange(),i={startColumn:r,startRow:l.startRow,endRow:l.startRow,endColumn:r};this._filterHeaderListener=this._refRangeService.watchRange(n,o,i,(s,a)=>{if(!a)this.terminate();else{const h=a.startColumn-s.startColumn;h!==0&&this._filterByModel.deltaCol(h)}})}async _setupByValues(e,r){this._disposePreviousModel();const n=e.getRange();if(n.startRow===n.endRow)return!1;const o=await ge.fromFilterColumn(this._injector,e,r);return this.filterByModel=o,this._filterBy$.next(c.FilterBy.VALUES),this._listenToFilterHeaderChange(e,r),!0}async _setupByColors(e,r){this._disposePreviousModel();const n=e.getRange();if(n.startRow===n.endRow)return!1;const o=await Se.fromFilterColumn(this._injector,e,r);return this.filterByModel=o,this._filterBy$.next(c.FilterBy.COLORS),this._listenToFilterHeaderChange(e,r),!0}_setupByConditions(e,r){this._disposePreviousModel();const n=e.getRange();if(n.startRow===n.endRow)return!1;const o=_e.fromFilterColumn(this._injector,e,r,e.getFilterColumn(r));return this.filterByModel=o,this._filterBy$.next(c.FilterBy.CONDITIONS),this._listenToFilterHeaderChange(e,r),!0}_disposePreviousModel(){var e;(e=this._filterByModel)==null||e.dispose(),this.filterByModel=null}};G=ve([ce(0,u.Inject(u.Injector)),ce(1,u.Inject(F.RefRangeService))],G);let _e=class extends u.Disposable{constructor(e,r,n,o,l){super();v(this,"canApply$",g.of(!0));v(this,"_conditionItem$");v(this,"conditionItem$");v(this,"_filterConditionFormParams$");v(this,"filterConditionFormParams$");this._filterModel=e,this.col=r,this._commandService=l,this._conditionItem$=new g.BehaviorSubject(n),this.conditionItem$=this._conditionItem$.asObservable(),this._filterConditionFormParams$=new g.BehaviorSubject(o),this.filterConditionFormParams$=this._filterConditionFormParams$.asObservable()}static fromFilterColumn(e,r,n,o){const[l,i]=f.testMappingFilterColumn(o==null?void 0:o.getColumnData());return e.createInstance(_e,r,n,l,i)}get conditionItem(){return this._conditionItem$.getValue()}get filterConditionFormParams(){return this._filterConditionFormParams$.getValue()}dispose(){super.dispose(),this._conditionItem$.complete(),this._filterConditionFormParams$.complete()}deltaCol(e){this.col+=e}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}async apply(){if(this._disposed)return!1;const e=f.mapToFilterColumn(this.conditionItem,this.filterConditionFormParams);return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:e})}onPrimaryConditionChange(e){const r=f.ALL_CONDITIONS.find(n=>n.operator===e);if(!r)throw new Error(`[ByConditionsModel]: condition item not found for operator: ${e}!`);this._conditionItem$.next(r),this._filterConditionFormParams$.next(f.getInitialFormParams(e))}onConditionFormChange(e){const r={...this.filterConditionFormParams,...e};if(r.and!==!0&&delete r.and,typeof e.and<"u"||typeof e.operator1<"u"||typeof e.operator2<"u"){const n=f.testMappingParams(r,this.conditionItem.numOfParameters);this._conditionItem$.next(n)}this._filterConditionFormParams$.next(r)}};_e=ve([ce(4,u.ICommandService)],_e);let ge=class extends u.Disposable{constructor(e,r,n,o,l){super();v(this,"_rawFilterItems$");v(this,"rawFilterItems$");v(this,"filterItems$");v(this,"_filterItems",[]);v(this,"_treeMapCache");v(this,"canApply$");v(this,"_manuallyUpdateFilterItems$");v(this,"_searchString$");v(this,"searchString$");this._filterModel=e,this.col=r,this._commandService=l,this._treeMapCache=o,this._searchString$=new g.BehaviorSubject(""),this.searchString$=this._searchString$.asObservable(),this._rawFilterItems$=new g.BehaviorSubject(n),this.rawFilterItems$=this._rawFilterItems$.asObservable(),this._manuallyUpdateFilterItems$=new g.Subject,this.filterItems$=g.merge(g.combineLatest([this._searchString$.pipe(g.throttleTime(500,void 0,{leading:!0,trailing:!0}),g.startWith(void 0)),this._rawFilterItems$]).pipe(g.map(([i,s])=>{if(!i)return s;const h=i.toLowerCase().split(/\s+/).filter(m=>!!m);return De(s,h)})),this._manuallyUpdateFilterItems$).pipe(g.shareReplay(1)),this.canApply$=this.filterItems$.pipe(g.map(i=>Ie(i).checked>0)),this.disposeWithMe(this.filterItems$.subscribe(i=>this._filterItems=i))}static async fromFilterColumn(e,r,n){const o=e.get(u.IUniverInstanceService),l=e.get(u.LocaleService),i=e.get(fe,u.Quantity.OPTIONAL),{unitId:s,subUnitId:a}=r,h=o.getUniverSheetInstance(s);if(!h)throw new Error(`[ByValuesModel]: Workbook not found for filter model with unitId: ${s}!`);const m=h==null?void 0:h.getSheetBySheetId(a);if(!m)throw new Error(`[ByValuesModel]: Worksheet not found for filter model with unitId: ${s} and subUnitId: ${a}!`);const p=r.getRange(),I=n,S=r.getFilterColumn(n),C=S==null?void 0:S.getColumnData().filters,O=new Set(C==null?void 0:C.filters),b=!!(C&&C.blank),P=r.getFilteredOutRowsExceptCol(n),L={...p,startRow:p.startRow+1,startColumn:I,endColumn:I};let A,M;if(i){const B=await i.getFilterValues({unitId:s,subUnitId:a,filteredOutRowsByOtherColumns:Array.from(P),filterColumn:S,filters:!!C,blankChecked:b,iterateRange:L,alreadyChecked:Array.from(O)});A=B.filterTreeItems,M=B.filterTreeMapCache}else{const B=ke(!!C,l,L,m,P,S,O,b,h.getStyles());A=B.filterTreeItems,M=B.filterTreeMapCache}return e.createInstance(ge,r,n,A,M)}get rawFilterItems(){return this._rawFilterItems$.getValue()}get filterItems(){return this._filterItems}get treeMapCache(){return this._treeMapCache}dispose(){this._rawFilterItems$.complete(),this._searchString$.complete()}deltaCol(e){this.col+=e}setSearchString(e){this._searchString$.next(e)}onCheckAllToggled(e){const r=u.Tools.deepClone(this._filterItems);r.forEach(n=>le(n,e)),this._manuallyUpdateFilterItems(r)}onFilterCheckToggled(e){const r=u.Tools.deepClone(this._filterItems),n=be(r,e.key);if(!n)return;const o=xe(n);le(n,!o),this._manuallyUpdateFilterItems(r)}onFilterOnly(e){const r=u.Tools.deepClone(this._filterItems);r.forEach(n=>le(n,!1)),e.forEach(n=>{const o=be(r,n);o&&le(o,!0)}),this._manuallyUpdateFilterItems(r)}_manuallyUpdateFilterItems(e){this._manuallyUpdateFilterItems$.next(e)}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}async apply(){if(this._disposed)return!1;const e=Ie(this._filterItems),{checked:r,checkedItems:n}=e,o=this.rawFilterItems;let l=0;for(const h of o)l+=h.count;const i=r===0,s=e.checked===l,a={colId:this.col};if(i)throw new Error("[ByValuesModel]: no checked items!");if(s)return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null});{a.filters={};const h=n.filter(p=>p.key!=="empty");h.length>0&&(a.filters={filters:h.flatMap(p=>p.originValues?Array.from(p.originValues):[p.title])}),h.length!==n.length&&(a.filters.blank=!0)}return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:a})}};ge=ve([ce(4,u.ICommandService)],ge);let Se=class extends u.Disposable{constructor(e,r,n,o,l){super();v(this,"canApply$",g.of(!0));v(this,"_cellFillColors$");v(this,"cellFillColors$");v(this,"_cellTextColors$");v(this,"cellTextColors$");this._filterModel=e,this.col=r,this._commandService=l,this._cellFillColors$=new g.BehaviorSubject(Array.from(n.values())),this.cellFillColors$=this._cellFillColors$.asObservable(),this._cellTextColors$=new g.BehaviorSubject(Array.from(o.values())),this.cellTextColors$=this._cellTextColors$.asObservable()}static async fromFilterColumn(e,r,n){var L,A,M;const o=e.get(u.IUniverInstanceService),{unitId:l,subUnitId:i}=r,s=o.getUniverSheetInstance(l);if(!s)throw new Error(`[ByColorsModel]: Workbook not found for filter model with unitId: ${l}!`);const a=s==null?void 0:s.getSheetBySheetId(i);if(!a)throw new Error(`[ByColorsModel]: Worksheet not found for filter model with unitId: ${l} and subUnitId: ${i}!`);const h=r.getRange(),m=n,p=(L=r.getFilterColumn(n))==null?void 0:L.getColumnData().colorFilters,I=r.getFilteredOutRowsExceptCol(n),S={...h,startRow:h.startRow+1,startColumn:m,endColumn:m},C=new Map,O=new Set((A=p==null?void 0:p.cellFillColors)!=null?A:[]),b=new Map,P=new Set((M=p==null?void 0:p.cellTextColors)!=null?M:[]);for(const B of a.iterateByColumn(S,!1,!0)){const{row:X,col:ne,value:oe}=B;if(I.has(X))continue;const V=a.getComposedCellStyleByCellData(X,ne,oe);if(V.bg&&V.bg.rgb){const x=new u.ColorKit(V.bg.rgb).toRgbString();C.has(x)||C.set(x,{color:x,checked:O.has(x)})}else C.set("default-fill-color",{color:null,checked:O.has(null)});if(V.cl&&V.cl.rgb){const x=new u.ColorKit(V.cl.rgb).toRgbString();b.has(x)||b.set(x,{color:x,checked:P.has(x)})}else b.set("default-font-color",{color:K.COLOR_BLACK_RGB,checked:P.has(K.COLOR_BLACK_RGB)})}return e.createInstance(Se,r,n,C,b)}get cellFillColors(){return this._cellFillColors$.getValue()}get cellTextColors(){return this._cellTextColors$.getValue()}dispose(){super.dispose(),this._cellFillColors$.complete()}deltaCol(e){this.col+=e}clear(){return this._disposed?Promise.resolve(!1):this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null})}onFilterCheckToggled(e,r=!0){const n=r?this.cellFillColors:this.cellTextColors,o=[];let l=!1;for(let i=0;i<n.length;i++){const s=n[i];if(s.color===e.color){l=!0,o.push({color:s.color,checked:!s.checked});continue}o.push({color:s.color,checked:s.checked})}l&&(this._resetColorsCheckedStatus(!r),r?this._cellFillColors$.next([...o]):this._cellTextColors$.next([...o]))}_resetColorsCheckedStatus(e=!0){const r=e?this.cellFillColors:this.cellTextColors,n=[];for(let o=0;o<r.length;o++)n.push({color:r[o].color,checked:!1});e?this._cellFillColors$.next([...n]):this._cellTextColors$.next([...n])}async apply(){if(this._disposed)return!1;const e=this.cellFillColors.filter(o=>o.checked).map(o=>o.color),r=this.cellTextColors.filter(o=>o.checked).map(o=>o.color);if(e.length===0&&r.length===0)return this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:null});const n={colId:this.col};return e.length>0?n.colorFilters={cellFillColors:e}:r.length>0&&(n.colorFilters={cellTextColors:r}),this._commandService.executeCommand(c.SetSheetsFilterCriteriaCommand.id,{unitId:this._filterModel.unitId,subUnitId:this._filterModel.subUnitId,col:this.col,criteria:n})}};Se=ve([ce(4,u.ICommandService)],Se);const ee="FILTER_PANEL_OPENED",ue={id:"sheet.operation.open-filter-panel",type:u.CommandType.OPERATION,handler:(t,e)=>{const r=t.get(u.IContextService),n=t.get(c.SheetsFilterService),o=t.get(G);t.get(u.ICommandService).syncExecuteCommand($.SetCellEditVisibleOperation.id,{visible:!1});const{unitId:i,subUnitId:s,col:a}=e,h=n.getFilterModel(i,s);return h?(o.setupCol(h,a),r.getContextValue(ee)||r.setContextValue(ee,!0),!0):!1}},te={id:"sheet.operation.close-filter-panel",type:u.CommandType.OPERATION,handler:t=>{const e=t.get(u.IContextService),r=t.get(G),n=t.get(E.ILayoutService,u.Quantity.OPTIONAL);return e.getContextValue(ee)?(e.setContextValue(ee,!1),n==null||n.focus(),r.terminate()):!1}},Pe={id:"sheet.operation.apply-filter",type:u.CommandType.OPERATION,handler:(t,e)=>{const{filterBy:r}=e;return t.get(G).changeFilterBy(r)}},He="sheets-filter-ui.config",Ce={};var ct=Object.getOwnPropertyDescriptor,ut=(t,e,r,n)=>{for(var o=n>1?void 0:n?ct(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},re=(t,e)=>(r,n)=>e(r,n,t);let ie=class extends u.Disposable{constructor(t,e,r,n,o,l){super(),this._sheetsFilterService=t,this._localeService=e,this._commandService=r,this._sheetPermissionCheckPermission=n,this._injector=o,this._sheetsSelectionService=l,this._commandExecutedListener()}_commandExecutedListener(){this.disposeWithMe(this._commandService.beforeCommandExecuted(t=>{var e,r,n;if(t.id===c.SmartToggleSheetsFilterCommand.id){const o=this._injector.get(u.IUniverInstanceService),l=F.getSheetCommandTarget(o);if(!l)return;const{unitId:i,subUnitId:s,worksheet:a}=l,h=(e=this._sheetsFilterService.getFilterModel(i,s))==null?void 0:e.getRange();let m;if(h)m=this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[F.RangeProtectionPermissionViewPoint],worksheetTypes:[F.WorksheetFilterPermission,F.WorksheetViewPermission]},[h]);else{const p=(r=this._sheetsSelectionService.getCurrentLastSelection())==null?void 0:r.range;if(p){let I={...p};I=p.startColumn===p.endColumn&&p.startRow===p.endRow?F.expandToContinuousRange(I,{left:!0,right:!0,up:!0,down:!0},a):I,m=this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[F.RangeProtectionPermissionViewPoint],worksheetTypes:[F.WorksheetViewPermission,F.WorksheetFilterPermission]},[I],i,s)}else m=this._sheetPermissionCheckPermission.permissionCheckWithoutRange({rangeTypes:[F.RangeProtectionPermissionViewPoint],worksheetTypes:[F.WorksheetViewPermission,F.WorksheetFilterPermission]})}m||this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr"))}if(t.id===ue.id){const o=t.params,{unitId:l,subUnitId:i}=o,s=(n=this._sheetsFilterService.getFilterModel(l,i))==null?void 0:n.getRange(),a=u.Tools.deepClone(s);a&&(a.startColumn=o.col,a.endColumn=o.col,this._sheetPermissionCheckPermission.permissionCheckWithRanges({rangeTypes:[F.RangeProtectionPermissionViewPoint],worksheetTypes:[F.WorksheetFilterPermission,F.WorksheetViewPermission]},[a])||this._sheetPermissionCheckPermission.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.filterErr")))}}))}};ie=ut([re(0,u.Inject(c.SheetsFilterService)),re(1,u.Inject(u.LocaleService)),re(2,u.ICommandService),re(3,u.Inject(F.SheetPermissionCheckController)),re(4,u.Inject(u.Injector)),re(5,u.Inject(F.SheetsSelectionsService))],ie);const Y=16,We=new Path2D("M4 6L8 10L12 6Z");class je{static drawNoCriteria(e,r,n,o){e.save(),K.Rect.drawWith(e,{radius:2,width:Y,height:Y,fill:o}),e.scale(r/Y,r/Y),e.fillStyle=n,e.fill(We),e.restore()}static drawHasCriteria(e,r,n,o){e.save(),K.Rect.drawWith(e,{radius:2,width:Y,height:Y,fill:o}),e.scale(r/Y,r/Y),e.fillStyle=n,e.fill(We),e.restore()}}var ht=Object.getOwnPropertyDescriptor,dt=(t,e,r,n)=>{for(var o=n>1?void 0:n?ht(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},Ae=(t,e)=>(r,n)=>e(r,n,t);const j=16,we=1;let Re=class extends K.Shape{constructor(e,r,n,o,l){super(e,r);v(this,"_cellWidth",0);v(this,"_cellHeight",0);v(this,"_filterParams");v(this,"_hovered",!1);this._contextService=n,this._commandService=o,this._themeService=l,this.setShapeProps(r),this.onPointerDown$.subscribeEvent(i=>this.onPointerDown(i)),this.onPointerEnter$.subscribeEvent(()=>this.onPointerEnter()),this.onPointerLeave$.subscribeEvent(()=>this.onPointerLeave())}setShapeProps(e){typeof e.cellHeight<"u"&&(this._cellHeight=e.cellHeight),typeof e.cellWidth<"u"&&(this._cellWidth=e.cellWidth),typeof e.filterParams<"u"&&(this._filterParams=e.filterParams),this.transformByState({width:e.width,height:e.height})}_draw(e){const r=this._cellHeight,n=this._cellWidth,o=j-n,l=j-r;e.save();const i=new Path2D;i.rect(o,l,n,r),e.clip(i);const{hasCriteria:s}=this._filterParams,a=this._themeService.getColorFromTheme("primary.600"),h=this._hovered?this._themeService.getColorFromTheme("gray.50"):"rgba(255, 255, 255, 1.0)";s?je.drawHasCriteria(e,j,a,h):je.drawNoCriteria(e,j,a,h),e.restore()}onPointerDown(e){if(e.button===2)return;const{col:r,unitId:n,subUnitId:o}=this._filterParams;this._contextService.getContextValue(ee)||!this._commandService.hasCommand(ue.id)||setTimeout(()=>{this._commandService.executeCommand(ue.id,{unitId:n,subUnitId:o,col:r})},200)}onPointerEnter(){this._hovered=!0,this.makeDirty(!0)}onPointerLeave(){this._hovered=!1,this.makeDirty(!0)}};Re=dt([Ae(2,u.IContextService),Ae(3,u.ICommandService),Ae(4,u.Inject(u.ThemeService))],Re);var mt=Object.getOwnPropertyDescriptor,pt=(t,e,r,n)=>{for(var o=n>1?void 0:n?mt(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},Z=(t,e)=>(r,n)=>e(r,n,t);const ft=1e3,vt=5e3;let Le=class extends u.RxDisposable{constructor(e,r,n,o,l,i,s,a){super();v(this,"_filterRangeShape",null);v(this,"_buttonRenderDisposable",null);v(this,"_filterButtonShapes",[]);this._context=e,this._injector=r,this._sheetSkeletonManagerService=n,this._sheetsFilterService=o,this._themeService=l,this._sheetInterceptorService=i,this._commandService=s,this._selectionRenderService=a,this._initRenderer()}dispose(){super.dispose(),this._disposeRendering()}_initRenderer(){this._sheetSkeletonManagerService.currentSkeleton$.pipe(g.switchMap(e=>{var s,a;if(!e)return g.of(null);const{unit:r,unitId:n}=this._context,o=((s=r.getActiveSheet())==null?void 0:s.getSheetId())||"",l=(a=this._sheetsFilterService.getFilterModel(n,o))!=null?a:void 0,i=()=>({unitId:n,worksheetId:o,filterModel:l,range:l==null?void 0:l.getRange(),skeleton:e.skeleton});return u.fromCallback(this._commandService.onCommandExecuted.bind(this._commandService)).pipe(g.filter(([h])=>{var m;return h.type===u.CommandType.MUTATION&&((m=h.params)==null?void 0:m.unitId)===r.getUnitId()&&(c.FILTER_MUTATIONS.has(h.id)||h.id===F.SetRangeValuesMutation.id)}),g.throttleTime(20,void 0,{leading:!1,trailing:!0}),g.map(i),g.startWith(i()))}),g.takeUntil(this.dispose$)).subscribe(e=>{this._disposeRendering(),!(!e||!e.range)&&(this._renderRange(e.range,e.skeleton),this._renderButtons(e))})}_renderRange(e,r){const{scene:n}=this._context,{rowHeaderWidth:o,columnHeaderHeight:l}=r,i=this._filterRangeShape=new $.SelectionControl(n,ft,this._themeService,{rowHeaderWidth:o,columnHeaderHeight:l,enableAutoFill:!1,highlightHeader:!1}),s={range:e,primary:null,style:{fill:"rgba(0, 0, 0, 0.0)"}},a=$.attachSelectionWithCoord(s,r);i.updateRangeBySelectionWithCoord(a),i.setEvent(!1),n.makeDirty(!0)}_renderButtons(e){const{range:r,filterModel:n,unitId:o,skeleton:l,worksheetId:i}=e,{scene:s}=this._context;this._interceptCellContent(o,i,e.range);const{startColumn:a,endColumn:h,startRow:m}=r;for(let p=a;p<=h;p++){const I=`sheets-filter-button-${p}`,S=$.getCoordByCell(m,p,s,l),{startX:C,startY:O,endX:b,endY:P}=S,L=b-C,A=P-O;if(A<=we||L<=we)continue;const M=!!n.getFilterColumn(p),B=b-j-we,X=O+(A-j)/2,ne={left:B,top:X,height:j,width:j,zIndex:vt,cellHeight:A,cellWidth:L,filterParams:{unitId:o,subUnitId:i,col:p,hasCriteria:M}},oe=this._injector.createInstance(Re,I,ne);this._filterButtonShapes.push(oe)}s.addObjects(this._filterButtonShapes),s.makeDirty()}_interceptCellContent(e,r,n){const{startRow:o,startColumn:l,endColumn:i}=n;this._buttonRenderDisposable=this._sheetInterceptorService.intercept(F.INTERCEPTOR_POINT.CELL_CONTENT,{effect:u.InterceptorEffectEnum.Style,handler:(s,a,h)=>{const{row:m,col:p,unitId:I,subUnitId:S}=a;return I!==e||S!==r||m!==o||p<l||p>i||((!s||s===a.rawData)&&(s={...a.rawData}),s.fontRenderExtension={...s==null?void 0:s.fontRenderExtension,rightOffset:j}),h(s)},priority:10})}_disposeRendering(){var e,r;(e=this._filterRangeShape)==null||e.dispose(),this._filterButtonShapes.forEach(n=>n.dispose()),(r=this._buttonRenderDisposable)==null||r.dispose(),this._filterRangeShape=null,this._buttonRenderDisposable=null,this._filterButtonShapes=[]}};Le=pt([Z(1,u.Inject(u.Injector)),Z(2,u.Inject($.SheetSkeletonManagerService)),Z(3,u.Inject(c.SheetsFilterService)),Z(4,u.Inject(u.ThemeService)),Z(5,u.Inject(F.SheetInterceptorService)),Z(6,u.ICommandService),Z(7,$.ISheetSelectionRenderService)],Le);var _t=Object.getOwnPropertyDescriptor,gt=(t,e,r,n)=>{for(var o=n>1?void 0:n?_t(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},Ve=(t,e)=>(r,n)=>e(r,n,t);let he=class extends u.RxDisposable{constructor(t,e){super(),this._renderManagerService=t,this._sheetsRenderService=e,[c.SetSheetsFilterRangeMutation,c.SetSheetsFilterCriteriaMutation,c.RemoveSheetsFilterMutation,c.ReCalcSheetsFilterMutation].forEach(r=>this.disposeWithMe(this._sheetsRenderService.registerSkeletonChangingMutations(r.id))),this.disposeWithMe(this._renderManagerService.registerRenderModule(u.UniverInstanceType.UNIVER_SHEET,[Le]))}};he=gt([Ve(0,K.IRenderManagerService),Ve(1,u.Inject($.SheetsRenderService))],he);var St=Object.defineProperty,Ct=Object.getOwnPropertyDescriptor,Tt=(t,e,r)=>e in t?St(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Et=(t,e,r,n)=>{for(var o=n>1?void 0:n?Ct(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},Qe=(t,e)=>(r,n)=>e(r,n,t),Ge=(t,e,r)=>Tt(t,typeof e!="symbol"?e+"":e,r);const Ot="SHEET_FILTER_UI_PLUGIN";_.UniverSheetsFilterMobileUIPlugin=class extends u.Plugin{constructor(e=Ce,r,n){super(),this._config=e,this._injector=r,this._configService=n;const{menu:o,...l}=u.merge({},Ce,this._config);o&&this._configService.setConfig("menu",o,{merge:!0}),this._configService.setConfig(He,l)}onStarting(){[[ie],[he]].forEach(e=>this._injector.add(e))}onReady(){this._injector.get(ie)}onRendered(){this._injector.get(he)}},Ge(_.UniverSheetsFilterMobileUIPlugin,"type",u.UniverInstanceType.UNIVER_SHEET),Ge(_.UniverSheetsFilterMobileUIPlugin,"pluginName",Ot),_.UniverSheetsFilterMobileUIPlugin=Et([u.DependentOn(c.UniverSheetsFilterPlugin),Qe(1,u.Inject(u.Injector)),Qe(2,u.IConfigService)],_.UniverSheetsFilterMobileUIPlugin);var k=function(){return k=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++){e=arguments[r];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t},k.apply(this,arguments)},It=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]]);return r},de=y.forwardRef(function(t,e){var r=t.icon,n=t.id,o=t.className,l=t.extend,i=It(t,["icon","id","className","extend"]),s="univerjs-icon univerjs-icon-".concat(n," ").concat(o||"").trim(),a=y.useRef("_".concat(Nt()));return Ye(r,"".concat(n),{defIds:r.defIds,idSuffix:a.current},k({ref:e,className:s},i),l)});function Ye(t,e,r,n,o){return y.createElement(t.tag,k(k({key:e},yt(t,r,o)),n),(Ft(t,r).children||[]).map(function(l,i){return Ye(l,"".concat(e,"-").concat(t.tag,"-").concat(i),r,void 0,o)}))}function yt(t,e,r){var n=k({},t.attrs);r!=null&&r.colorChannel1&&n.fill==="colorChannel1"&&(n.fill=r.colorChannel1),t.tag==="mask"&&n.id&&(n.id=n.id+e.idSuffix),Object.entries(n).forEach(function(l){var i=l[0],s=l[1];i==="mask"&&typeof s=="string"&&(n[i]=s.replace(/url\(#(.*)\)/,"url(#$1".concat(e.idSuffix,")")))});var o=e.defIds;return!o||o.length===0||(t.tag==="use"&&n["xlink:href"]&&(n["xlink:href"]=n["xlink:href"]+e.idSuffix),Object.entries(n).forEach(function(l){var i=l[0],s=l[1];typeof s=="string"&&(n[i]=s.replace(/url\(#(.*)\)/,"url(#$1".concat(e.idSuffix,")")))})),n}function Ft(t,e){var r,n=e.defIds;return!n||n.length===0?t:t.tag==="defs"&&(!((r=t.children)===null||r===void 0)&&r.length)?k(k({},t),{children:t.children.map(function(o){return typeof o.attrs.id=="string"&&n&&n.includes(o.attrs.id)?k(k({},o),{attrs:k(k({},o.attrs),{id:o.attrs.id+e.idSuffix})}):o})}):t}function Nt(){return Math.random().toString(36).substring(2,8)}de.displayName="UniverIcon";var bt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10 1.05957C10.356 1.05957 10.6816 1.26162 10.8408 1.58008L18.8408 17.5801L18.8799 17.668C19.0486 18.1134 18.8551 18.6232 18.4199 18.8408C17.9557 19.0727 17.3913 18.8841 17.1592 18.4199L10 4.10156L2.84082 18.4199C2.60871 18.8841 2.04434 19.0727 1.58008 18.8408C1.11587 18.6087 0.92731 18.0443 1.15918 17.5801L9.15918 1.58008C9.31841 1.26162 9.64395 1.05957 10 1.05957Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M15.3337 11.7261L15.4294 11.731C15.9035 11.779 16.2732 12.1798 16.2732 12.6665C16.2732 13.1532 15.9035 13.554 15.4294 13.602L15.3337 13.6069H4.66675C4.1476 13.6069 3.72632 13.1856 3.72632 12.6665C3.72632 12.1474 4.1476 11.7261 4.66675 11.7261H15.3337Z"}}]},qe=y.forwardRef(function(t,e){return y.createElement(de,Object.assign({},t,{id:"a-icon",ref:e,icon:bt}))});qe.displayName="AIcon";var Pt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M17.0596 10C17.0596 6.10087 13.8992 2.94043 10 2.94043C6.10087 2.94043 2.94043 6.10087 2.94043 10C2.94043 13.8992 6.10087 17.0596 10 17.0596C13.8992 17.0596 17.0596 13.8992 17.0596 10ZM18.9404 10C18.9404 14.9374 14.9374 18.9404 10 18.9404C5.06257 18.9404 1.05957 14.9374 1.05957 10C1.05957 5.06257 5.06257 1.05957 10 1.05957C14.9374 1.05957 18.9404 5.06257 18.9404 10Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M4.29492 4.13476C4.63911 3.79057 5.1845 3.76906 5.55371 4.07031L5.625 4.13476L16.0244 14.5352L16.0889 14.6064C16.3902 14.9757 16.3686 15.52 16.0244 15.8643C15.6573 16.2313 15.0624 16.2313 14.6953 15.8643L4.29492 5.46484L4.23047 5.39355C3.92922 5.02434 3.95073 4.47895 4.29492 4.13476Z"}}]},Ke=y.forwardRef(function(t,e){return y.createElement(de,Object.assign({},t,{id:"ban-icon",ref:e,icon:Pt}))});Ke.displayName="BanIcon";var At={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3.32182 2.60967C2.98161 2.60967 2.79671 3.0074 3.01601 3.2675L6.85819 7.8246C6.94943 7.93282 6.99947 8.06981 6.99947 8.21136V12.7338C6.99947 12.898 7.0998 13.0455 7.2525 13.1058L8.73833 13.6928C9.00085 13.7965 9.28531 13.6031 9.28531 13.3208V8.21136C9.28531 8.06981 9.33535 7.93282 9.42659 7.8246L13.2688 3.2675C13.4881 3.0074 13.3032 2.60967 12.963 2.60967H3.32182ZM2.09858 4.04101C1.22139 3.0006 1.96097 1.40967 3.32182 1.40967H12.963C14.3238 1.40967 15.0634 3.0006 14.1862 4.04101L10.4853 8.43054V13.3208C10.4853 14.4498 9.34747 15.2237 8.29742 14.8089L6.81158 14.2219C6.20078 13.9806 5.79947 13.3905 5.79947 12.7338V8.43054L2.09858 4.04101Z",fillRule:"evenodd",clipRule:"evenodd"}}]},Ze=y.forwardRef(function(t,e){return y.createElement(de,Object.assign({},t,{id:"filter-icon",ref:e,icon:At}))});Ze.displayName="FilterIcon";var wt={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM11.7245 6.42417C11.9588 6.18985 11.9588 5.80995 11.7245 5.57564C11.4901 5.34132 11.1102 5.34132 10.8759 5.57564L7.3002 9.15137L5.72446 7.57564C5.49014 7.34132 5.11025 7.34132 4.87593 7.57564C4.64162 7.80995 4.64162 8.18985 4.87593 8.42417L6.87593 10.4242C7.11025 10.6585 7.49014 10.6585 7.72446 10.4242L11.7245 6.42417Z",fillRule:"evenodd",clipRule:"evenodd"}}]},Xe=y.forwardRef(function(t,e){return y.createElement(de,Object.assign({},t,{id:"success-icon",ref:e,icon:wt}))});Xe.displayName="SuccessIcon";function Rt(t){const{model:e}=t,r=E.useDependency(u.LocaleService),n=E.useObservable(e.cellFillColors$,[],!0),o=E.useObservable(e.cellTextColors$,[],!0),l=y.useCallback(s=>{e.onFilterCheckToggled(s)},[e]),i=y.useCallback(s=>{e.onFilterCheckToggled(s,!1)},[e]);return d.jsx("div",{"data-u-comp":"sheets-filter-panel-colors-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:d.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:N.clsx("univer-mt-2 univer-box-border univer-flex univer-h-[300px] univer-flex-grow univer-flex-col univer-gap-4 univer-overflow-auto univer-rounded-md univer-px-2 univer-py-2.5",N.borderClassName),children:[n.length>1&&d.jsxs("div",{children:[d.jsx("div",{className:"univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",children:r.t("sheets-filter.panel.filter-by-cell-fill-color")}),d.jsx("div",{className:"univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",children:n.map((s,a)=>d.jsxs("div",{className:"univer-relative univer-h-6 univer-w-6",onClick:()=>l(s),children:[s.color?d.jsx("button",{type:"button",className:N.clsx("univer-box-border univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full univer-border univer-border-solid univer-border-transparent univer-bg-gray-300 univer-transition-shadow hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"),style:{backgroundColor:s.color}}):d.jsx(Ke,{className:"univer-h-6 univer-w-6 univer-cursor-pointer univer-rounded-full hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white"}),s.checked&&d.jsx(ze,{})]},`sheets-filter-cell-fill-color-${a}`))})]}),o.length>1&&d.jsxs("div",{children:[d.jsx("div",{className:"univer-mb-2 univer-text-sm univer-text-gray-900 dark:!univer-text-white",children:r.t("sheets-filter.panel.filter-by-cell-text-color")}),d.jsx("div",{className:"univer-grid univer-grid-cols-8 univer-items-center univer-justify-start univer-gap-2",children:o.map((s,a)=>d.jsxs("div",{className:"univer-relative univer-h-6 univer-w-6",onClick:()=>i(s),children:[d.jsx("div",{className:"univer-box-border univer-flex univer-h-full univer-w-full univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-border univer-border-solid univer-border-[rgba(13,13,13,0.06)] univer-p-0.5 hover:univer-ring-2 hover:univer-ring-offset-2 hover:univer-ring-offset-white dark:!univer-border-[rgba(255,255,255,0.06)]",children:d.jsx(qe,{style:{color:s.color}})}),s.checked&&d.jsx(ze,{})]},`sheets-filter-cell-text-color-${a}`))})]}),n.length<=1&&o.length<=1&&d.jsx("div",{className:"univer-flex univer-h-full univer-w-full univer-items-center univer-justify-center univer-text-sm univer-text-gray-900 dark:!univer-text-gray-200",children:r.t("sheets-filter.panel.filter-by-color-none")})]})})}function ze(){return d.jsx("div",{className:"univer-absolute -univer-bottom-0.5 -univer-right-0.5 univer-flex univer-h-3 univer-w-3 univer-cursor-pointer univer-items-center univer-justify-center univer-rounded-full univer-bg-white",children:d.jsx(Xe,{className:"univer-h-full univer-w-full univer-font-bold univer-text-[#418F1F]"})})}function Lt(t){var S,C;const{model:e}=t,r=E.useDependency(u.LocaleService),n=E.useObservable(e.conditionItem$,void 0),o=E.useObservable(e.filterConditionFormParams$,void 0),l=o!=null&&o.and?"AND":"OR",i=y.useCallback(O=>{e.onConditionFormChange({and:O==="AND"})},[e]),s=Mt(r),a=y.useCallback(O=>{e.onPrimaryConditionChange(O)},[e]),h=$t(r),m=y.useCallback(O=>{e.onConditionFormChange(O)},[e]),p=r.t("sheets-filter.panel.input-values-placeholder");function I(O,b,P){const L=f.getItemByOperator(O).numOfParameters===1;return d.jsxs(d.Fragment,{children:[P==="operator2"&&d.jsxs(N.RadioGroup,{value:l,onChange:i,children:[d.jsx(N.Radio,{value:"AND",children:r.t("sheets-filter.panel.and")}),d.jsx(N.Radio,{value:"OR",children:r.t("sheets-filter.panel.or")})]}),d.jsx(N.Select,{value:O,options:h,onChange:A=>m({[P]:A})}),L&&d.jsx("div",{children:d.jsx(N.Input,{className:"univer-mt-2",value:b,placeholder:p,onChange:A=>m({[P==="operator1"?"val1":"val2"]:A})})})]})}return d.jsx("div",{"data-u-comp":"sheets-filter-panel-conditions-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:n&&o&&d.jsxs(d.Fragment,{children:[d.jsx(N.Select,{value:n.operator,options:s,onChange:a}),f.getItemByOperator(n.operator).numOfParameters!==0?d.jsxs("div",{"data-u-comp":"sheets-filter-panel-conditions-container-inner",className:N.clsx("univer-mt-2 univer-flex-grow univer-overflow-hidden univer-rounded-md univer-p-2",N.borderClassName),children:[n.numOfParameters>=1&&I(o.operator1,(S=o.val1)!=null?S:"","operator1"),n.numOfParameters>=2&&I(o.operator2,(C=o.val2)!=null?C:"","operator2"),d.jsxs("div",{"data-u-comp":"sheets-filter-panel-conditions-desc",className:"univer-mt-2 univer-text-xs univer-text-gray-500",children:[r.t("sheets-filter.panel.?"),d.jsx("br",{}),r.t("sheets-filter.panel.*")]})]}):null]})})}function Mt(t){const e=t.getCurrentLocale();return y.useMemo(()=>[{options:[{label:t.t(f.NONE.label),value:f.NONE.operator}]},{options:[{label:t.t(f.EMPTY.label),value:f.EMPTY.operator},{label:t.t(f.NOT_EMPTY.label),value:f.NOT_EMPTY.operator}]},{options:[{label:t.t(f.TEXT_CONTAINS.label),value:f.TEXT_CONTAINS.operator},{label:t.t(f.DOES_NOT_CONTAIN.label),value:f.DOES_NOT_CONTAIN.operator},{label:t.t(f.STARTS_WITH.label),value:f.STARTS_WITH.operator},{label:t.t(f.ENDS_WITH.label),value:f.ENDS_WITH.operator},{label:t.t(f.EQUALS.label),value:f.EQUALS.operator}]},{options:[{label:t.t(f.GREATER_THAN.label),value:f.GREATER_THAN.operator},{label:t.t(f.GREATER_THAN_OR_EQUAL.label),value:f.GREATER_THAN_OR_EQUAL.operator},{label:t.t(f.LESS_THAN.label),value:f.LESS_THAN.operator},{label:t.t(f.LESS_THAN_OR_EQUAL.label),value:f.LESS_THAN_OR_EQUAL.operator},{label:t.t(f.EQUAL.label),value:f.EQUAL.operator},{label:t.t(f.NOT_EQUAL.label),value:f.NOT_EQUAL.operator},{label:t.t(f.BETWEEN.label),value:f.BETWEEN.operator},{label:t.t(f.NOT_BETWEEN.label),value:f.NOT_BETWEEN.operator}]},{options:[{label:t.t(f.CUSTOM.label),value:f.CUSTOM.operator}]}],[e,t])}function $t(t){const e=t.getCurrentLocale();return y.useMemo(()=>f.ALL_CONDITIONS.filter(r=>r.numOfParameters!==2).map(r=>({label:t.t(r.label),value:r.operator})),[e,t])}function Ut(t){const{model:e}=t,r=E.useDependency(u.LocaleService),n=E.useObservable(e.searchString$,"",!0),o=E.useObservable(e.filterItems$,void 0,!0),l=r.t("sheets-filter.panel.filter-only"),i=Ie(o),s=i.checked>0&&i.unchecked===0,a=i.checked>0&&i.unchecked>0,h=e.treeMapCache,m=y.useCallback(()=>{e.onCheckAllToggled(!s)},[e,s]),p=y.useCallback(S=>{e.setSearchString(S)},[e]);function I(S){let C=[];return S.forEach(O=>{O.checked&&C.push(O.key),O.children&&(C=C.concat(I(O.children)))}),C}return d.jsxs("div",{"data-u-comp":"sheets-filter-panel-values-container",className:"univer-flex univer-h-full univer-min-h-[300px] univer-flex-col",children:[d.jsx(N.Input,{autoFocus:!0,value:n,placeholder:r.t("sheets-filter.panel.search-placeholder"),onChange:p}),d.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:N.clsx("univer-mt-2 univer-box-border univer-flex univer-flex-grow univer-flex-col univer-overflow-hidden univer-rounded-md univer-px-2 univer-py-2.5",N.borderClassName),children:[d.jsx("div",{"data-u-comp":"sheets-filter-panel-values-item",className:"univer-box-border univer-h-8 univer-w-full univer-py-0.5",children:d.jsxs("div",{"data-u-comp":"sheets-filter-panel-values-item-inner",className:"univer-box-border univer-flex univer-h-7 univer-items-center univer-rounded-md univer-pb-0 univer-pl-5 univer-pr-0.5 univer-pt-0 univer-text-sm",children:[d.jsx(N.Checkbox,{indeterminate:a,disabled:o.length===0,checked:s,onChange:m}),d.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-text",className:"univer-mx-1 univer-inline-block univer-flex-shrink univer-overflow-hidden univer-text-ellipsis univer-whitespace-nowrap univer-text-gray-900 dark:!univer-text-white",children:`${r.t("sheets-filter.panel.select-all")}`}),d.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-count",className:"univer-text-gray-400 dark:!univer-text-gray-500",children:`(${i.checked}/${i.checked+i.unchecked})`})]})}),d.jsx("div",{"data-u-comp":"sheets-filter-panel-values-virtual",className:"univer-flex-grow",children:d.jsx(N.Tree,{data:o,defaultExpandAll:!1,valueGroup:I(o),onChange:S=>{e.onFilterCheckToggled(S)},defaultCache:h,itemHeight:28,treeNodeClassName:`
                          univer-pr-2 univer-border-box univer-max-w-[245px] univer-rounded-md
                          [&:hover_a]:univer-inline-block
                          hover:univer-bg-gray-50 univer-h-full
                          univer-text-gray-900 dark:hover:!univer-bg-gray-900
                          dark:!univer-text-white
                        `,attachRender:S=>d.jsxs("div",{className:"univer-ml-1 univer-flex univer-h-5 univer-flex-1 univer-cursor-pointer univer-items-center univer-justify-between univer-text-sm univer-text-primary-500",children:[d.jsx("span",{"data-u-comp":"sheets-filter-panel-values-item-count",className:"univer-text-gray-400 dark:!univer-text-gray-500",children:`(${S.count})`}),d.jsx("a",{className:"univer-box-border univer-hidden univer-h-4 univer-whitespace-nowrap univer-px-1.5",onClick:()=>{const C=[];S.children?S.children.forEach(O=>{O.children?O.children.forEach(b=>{C.push(b.key)}):C.push(O.key)}):C.push(S.key),e.onFilterOnly(C)},children:l})]})})})]})]})}function kt(){var b;const t=E.useDependency(G),e=E.useDependency(u.LocaleService),r=E.useDependency(u.ICommandService),n=E.useObservable(t.filterBy$,void 0,!0),o=E.useObservable(t.filterByModel$,void 0,!1),l=E.useObservable(()=>(o==null?void 0:o.canApply$)||g.of(!1),void 0,!1,[o]),i=Bt(e),s=!E.useObservable(t.hasCriteria$),a=y.useCallback(P=>{r.executeCommand(Pe.id,{filterBy:P})},[r]),h=y.useCallback(async()=>{await(o==null?void 0:o.clear()),r.executeCommand(te.id)},[o,r]),m=y.useCallback(()=>{r.executeCommand(te.id)},[r]),p=y.useCallback(async()=>{await(o==null?void 0:o.apply()),r.executeCommand(te.id)},[o,r]),S=(b=E.useDependency(c.SheetsFilterService).activeFilterModel)==null?void 0:b.getRange(),C=t.col,O=E.useComponentsOfPart($.SheetsUIPart.FILTER_PANEL_EMBED_POINT);return d.jsxs("div",{"data-u-comp":"sheets-filter-panel",className:"univer-box-border univer-flex univer-max-h-[500px] univer-w-[400px] univer-flex-col univer-rounded-lg univer-bg-white univer-p-4 univer-shadow-lg dark:!univer-border-gray-600 dark:!univer-bg-gray-700",children:[d.jsx(E.ComponentContainer,{components:O,sharedProps:{range:S,colIndex:C,onClose:m}}),d.jsx("div",{className:"univer-mb-1 univer-flex-shrink-0 univer-flex-grow-0",children:d.jsx(N.Segmented,{value:n,items:i,onChange:P=>a(P)})}),o?d.jsx("div",{"data-u-comp":"sheets-filter-panel-content",className:"univer-flex-shrink univer-flex-grow univer-pt-2",children:n===c.FilterBy.VALUES?d.jsx(Ut,{model:o}):n===c.FilterBy.COLORS?d.jsx(Rt,{model:o}):d.jsx(Lt,{model:o})}):d.jsx("div",{className:"univer-flex-1"}),d.jsxs("div",{"data-u-comp":"sheets-filter-panel-footer",className:"univer-mt-4 univer-inline-flex univer-flex-shrink-0 univer-flex-grow-0 univer-flex-nowrap univer-justify-between univer-overflow-hidden",children:[d.jsx(N.Button,{variant:"link",onClick:h,disabled:s,children:e.t("sheets-filter.panel.clear-filter")}),d.jsxs("span",{className:"univer-flex univer-gap-2",children:[d.jsx(N.Button,{variant:"default",onClick:m,children:e.t("sheets-filter.panel.cancel")}),d.jsx(N.Button,{disabled:!l,variant:"primary",onClick:p,children:e.t("sheets-filter.panel.confirm")})]})]})]})}function Bt(t){const e=t.getCurrentLocale();return y.useMemo(()=>[{label:t.t("sheets-filter.panel.by-values"),value:c.FilterBy.VALUES},{label:t.t("sheets-filter.panel.by-colors"),value:c.FilterBy.COLORS},{label:t.t("sheets-filter.panel.by-conditions"),value:c.FilterBy.CONDITIONS}],[e,t])}function xt(t){const e=t.get(c.SheetsFilterService);return{id:c.SmartToggleSheetsFilterCommand.id,type:E.MenuItemType.BUTTON_SELECTOR,icon:"FilterIcon",tooltip:"sheets-filter.toolbar.smart-toggle-filter-tooltip",hidden$:E.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),activated$:e.activeFilterModel$.pipe(g.map(r=>!!r)),disabled$:$.getObservableWithExclusiveRange$(t,$.getCurrentRangeDisable$(t,{worksheetTypes:[F.WorksheetFilterPermission,F.WorksheetViewPermission],rangeTypes:[F.RangeProtectionPermissionViewPoint]}))}}function Dt(t){const e=t.get(c.SheetsFilterService);return{id:c.ClearSheetsFilterCriteriaCommand.id,type:E.MenuItemType.BUTTON,title:"sheets-filter.toolbar.clear-filter-criteria",hidden$:E.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),disabled$:e.activeFilterModel$.pipe(g.switchMap(r=>{var n;return(n=r==null?void 0:r.hasCriteria$.pipe(g.map(o=>!o)))!=null?n:g.of(!0)}))}}function Ht(t){const e=t.get(c.SheetsFilterService);return{id:c.ReCalcSheetsFilterCommand.id,type:E.MenuItemType.BUTTON,title:"sheets-filter.toolbar.re-calc-filter-conditions",hidden$:E.getMenuHiddenObservable(t,u.UniverInstanceType.UNIVER_SHEET),disabled$:e.activeFilterModel$.pipe(g.switchMap(r=>{var n;return(n=r==null?void 0:r.hasCriteria$.pipe(g.map(o=>!o)))!=null?n:g.of(!0)}))}}const Wt={[E.RibbonDataGroup.ORGANIZATION]:{[c.SmartToggleSheetsFilterCommand.id]:{order:2,menuItemFactory:xt,[c.ClearSheetsFilterCriteriaCommand.id]:{order:0,menuItemFactory:Dt},[c.ReCalcSheetsFilterCommand.id]:{order:1,menuItemFactory:Ht}}}},jt={id:c.SmartToggleSheetsFilterCommand.id,binding:E.KeyCode.L|E.MetaKeys.CTRL_COMMAND|E.MetaKeys.SHIFT,description:"sheets-filter.shortcut.smart-toggle-filter",preconditions:$.whenSheetEditorFocused,group:"4_sheet-edit"};var Vt=Object.getOwnPropertyDescriptor,Qt=(t,e,r,n)=>{for(var o=n>1?void 0:n?Vt(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},U=(t,e)=>(r,n)=>e(r,n,t);const Je="FILTER_PANEL_POPUP";let Te=class extends he{constructor(e,r,n,o,l,i,s,a,h,m,p,I,S){super(S,I);v(this,"_popupDisposable");this._injector=e,this._componentManager=r,this._sheetsFilterPanelService=n,this._sheetCanvasPopupService=o,this._sheetsFilterService=l,this._localeService=i,this._shortcutService=s,this._commandService=a,this._menuManagerService=h,this._contextService=m,this._messageService=p,this._initCommands(),this._initShortcuts(),this._initMenuItems(),this._initUI()}dispose(){super.dispose(),this._closeFilterPopup()}_initShortcuts(){[jt].forEach(e=>{this.disposeWithMe(this._shortcutService.registerShortcut(e))})}_initCommands(){[c.SmartToggleSheetsFilterCommand,c.RemoveSheetFilterCommand,c.SetSheetFilterRangeCommand,c.SetSheetsFilterCriteriaCommand,c.ClearSheetsFilterCriteriaCommand,c.ReCalcSheetsFilterCommand,Pe,ue,te].forEach(e=>{this.disposeWithMe(this._commandService.registerCommand(e))})}_initMenuItems(){this._menuManagerService.mergeMenu(Wt)}_initUI(){[[Je,kt],["FilterIcon",Ze]].forEach(([e,r])=>{this.disposeWithMe(this._componentManager.register(e,r))}),this.disposeWithMe(this._contextService.subscribeContextValue$(ee).pipe(g.distinctUntilChanged()).subscribe(e=>{e?this._openFilterPopup():this._closeFilterPopup()})),this.disposeWithMe(this._sheetsFilterService.errorMsg$.subscribe(e=>{e&&this._messageService.show({type:N.MessageType.Error,content:this._localeService.t(e)})}))}_openFilterPopup(){const e=this._sheetsFilterPanelService.filterModel;if(!e)throw new Error("[SheetsFilterUIController]: no filter model when opening filter popup!");const r=e.getRange(),n=this._sheetsFilterPanelService.col,{startRow:o}=r;this._popupDisposable=this._sheetCanvasPopupService.attachPopupToCell(o,n,{componentKey:Je,direction:"horizontal",onClickOutside:()=>this._commandService.syncExecuteCommand(te.id),offset:[5,0]})}_closeFilterPopup(){var e;(e=this._popupDisposable)==null||e.dispose(),this._popupDisposable=null}};Te=Qt([U(0,u.Inject(u.Injector)),U(1,u.Inject(E.ComponentManager)),U(2,u.Inject(G)),U(3,u.Inject($.SheetCanvasPopManagerService)),U(4,u.Inject(c.SheetsFilterService)),U(5,u.Inject(u.LocaleService)),U(6,E.IShortcutService),U(7,u.ICommandService),U(8,E.IMenuManagerService),U(9,u.IContextService),U(10,E.IMessageService),U(11,u.Inject($.SheetsRenderService)),U(12,K.IRenderManagerService)],Te);var Gt=Object.defineProperty,Yt=Object.getOwnPropertyDescriptor,qt=(t,e,r)=>e in t?Gt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Kt=(t,e,r,n)=>{for(var o=n>1?void 0:n?Yt(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},Me=(t,e)=>(r,n)=>e(r,n,t),et=(t,e,r)=>qt(t,typeof e!="symbol"?e+"":e,r);const Zt="SHEET_FILTER_UI_PLUGIN";_.UniverSheetsFilterUIPlugin=class extends u.Plugin{constructor(e=Ce,r,n,o){super(),this._config=e,this._injector=r,this._configService=n,this._rpcChannelService=o;const{menu:l,...i}=u.merge({},Ce,this._config);l&&this._configService.setConfig("menu",l,{merge:!0}),this._configService.setConfig(He,i)}onStarting(){u.registerDependencies(this._injector,[[G],[ie],[Te]]),this._config.useRemoteFilterValuesGenerator&&this._rpcChannelService&&this._injector.add([fe,{useFactory:()=>pe.toModule(this._rpcChannelService.requestChannel(Fe))}])}onReady(){u.touchDependencies(this._injector,[[ie]])}onRendered(){u.touchDependencies(this._injector,[[Te]])}},et(_.UniverSheetsFilterUIPlugin,"type",u.UniverInstanceType.UNIVER_SHEET),et(_.UniverSheetsFilterUIPlugin,"pluginName",Zt),_.UniverSheetsFilterUIPlugin=Kt([u.DependentOn(c.UniverSheetsFilterPlugin),Me(1,u.Inject(u.Injector)),Me(2,u.IConfigService),Me(3,u.Optional(pe.IRPCChannelService))],_.UniverSheetsFilterUIPlugin);var Xt=Object.getOwnPropertyDescriptor,zt=(t,e,r,n)=>{for(var o=n>1?void 0:n?Xt(e,r):e,l=t.length-1,i;l>=0;l--)(i=t[l])&&(o=i(o)||o);return o},tt=(t,e)=>(r,n)=>e(r,n,t);_.UniverSheetsFilterUIWorkerPlugin=(Ee=class extends u.Plugin{constructor(e,r,n){super(),this._config=e,this._injector=r,this._rpcChannelService=n}onStarting(){[[fe,{useClass:Ne}]].forEach(e=>this._injector.add(e))}onReady(){this._rpcChannelService.registerChannel(Fe,pe.fromModule(this._injector.get(fe)))}},v(Ee,"type",u.UniverInstanceType.UNIVER_SHEET),v(Ee,"pluginName","SHEET_FILTER_UI_WORKER_PLUGIN"),Ee),_.UniverSheetsFilterUIWorkerPlugin=zt([tt(1,u.Inject(u.Injector)),tt(2,pe.IRPCChannelService)],_.UniverSheetsFilterUIWorkerPlugin),_.ChangeFilterByOperation=Pe,_.CloseFilterPanelOperation=te,_.OpenFilterPanelOperation=ue,Object.defineProperty(_,Symbol.toStringTag,{value:"Module"})});
